"""
SMS Models for BFSI Backend

This module contains models for SMS functionality using Infobip API.
Follows the same patterns as WhatsApp and Email models for consistency.

Compatible with:
- Python 3.12.10
- Django 4.2
- Infobip API

Author: BFSI Backend Team
"""

import json
from django.db import models
from django.utils import timezone
from django_extensions.db.models import TimeStampedModel

from authorization.models import User
from odr.models import Dispute
from notice.models import Template





class SMSTemplate(models.Model):
    """
    Model to store SMS templates.
    Follows the same pattern as WhatsAppTemplate and EmailTemplate.
    """
    # Template identification
    sms_template_id = models.Char<PERSON>ield(max_length=100, null=True, blank=True, help_text="Infobip template ID")
    name = models.Char<PERSON>ield(max_length=255, help_text="Template name for identification")
    body = models.TextField(help_text="Full body of the SMS template")
    
    # Template configuration
    sender_id = models.Char<PERSON>ield(max_length=20, null=True, blank=True, help_text="Custom sender ID for this template")
    requires_attachment = models.<PERSON><PERSON>an<PERSON>ield(default=False, help_text="Whether this template requires file attachment")
    is_active = models.BooleanField(default=True, help_text="Whether this template is active")
    
    # SMS sending status fields (following WhatsApp pattern)
    sent_to_sms = models.BooleanField(default=False, help_text="Whether SMS has been sent")
    sms_processed_rows = models.IntegerField(default=0, null=True, blank=True, help_text="Number of rows processed")
    sms_messages_sent = models.IntegerField(default=0, null=True, blank=True, help_text="Number of SMS messages sent")
    sms_status = models.CharField(max_length=50, null=True, blank=True, help_text="Current SMS sending status")
    sms_errors = models.JSONField(default=list, null=True, blank=True, help_text="List of SMS sending errors")
    
    # Notice type flags (following existing pattern)
    is_s21_notice = models.BooleanField(default=False, help_text="Section 21 notice flag")
    is_s138_notice = models.BooleanField(default=False, help_text="Section 138 notice flag")
    
    # Conciliation notice fields (following existing pattern)
    parameter_mapping = models.JSONField(default=dict, null=True, blank=True, help_text="Parameter mapping for template")
    is_termination_notice = models.BooleanField(default=False, help_text="Termination notice flag")
    is_payment_request_notice = models.BooleanField(default=False, null=True, blank=True, help_text="Payment request notice flag")
    has_body_params = models.BooleanField(default=False, null=True, blank=True, help_text="Whether template has body parameters")
    conciliation_notice_1 = models.BooleanField(default=False, null=True, blank=True, help_text="Conciliation notice 1 flag")
    conciliation_notice_2 = models.BooleanField(default=False, null=True, blank=True, help_text="Conciliation notice 2 flag")
    conciliation_notice_3 = models.BooleanField(default=False, null=True, blank=True, help_text="Conciliation notice 3 flag")
    conciliation_notice_4 = models.BooleanField(default=False, null=True, blank=True, help_text="Conciliation notice 4 flag")
    
    # Timestamps (following existing pattern)
    created_at = models.DateTimeField(default=timezone.now, help_text="Template creation timestamp")
    updated_at = models.DateTimeField(auto_now=True, help_text="Template last update timestamp")
    created_by = models.ForeignKey(User, on_delete=models.RESTRICT, null=True, blank=True, help_text="User who created this template")
    
    def save(self, *args, **kwargs):
        """Ensure created_at is timezone-aware UTC (following existing pattern)."""
        if self.created_at and timezone.is_naive(self.created_at):
            self.created_at = timezone.make_aware(self.created_at, timezone.utc)
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"{self.name} ({self.sms_template_id or 'No ID'})"
    
    class Meta:
        verbose_name = "SMS Template"
        verbose_name_plural = "SMS Templates"
        ordering = ['-created_at']


class SMSMessage(models.Model):
    """
    Model to track individual SMS messages sent.
    Similar to SendgridMail model but for SMS.
    """
    # Message identification
    phone_number = models.CharField(max_length=20, help_text="Recipient phone number")
    message_id = models.CharField(max_length=120, unique=True, null=True, blank=True, help_text="Infobip message ID")
    
    # Message content
    message_text = models.TextField(help_text="SMS message content")
    sender_id = models.CharField(max_length=20, null=True, blank=True, help_text="Sender ID used")
    
    # Status tracking
    status = models.CharField(max_length=50, default='PENDING', help_text="Message delivery status")
    status_description = models.TextField(null=True, blank=True, help_text="Detailed status description")
    
    # Delivery tracking
    sent_timestamp = models.DateTimeField(null=True, blank=True, help_text="When message was sent")
    delivered_timestamp = models.DateTimeField(null=True, blank=True, help_text="When message was delivered")
    failed_timestamp = models.DateTimeField(null=True, blank=True, help_text="When message failed")
    
    # Relationships (following existing pattern)
    dispute = models.ForeignKey(Dispute, on_delete=models.CASCADE, null=True, blank=True, help_text="Related dispute")
    template = models.ForeignKey(Template, on_delete=models.CASCADE, null=True, blank=True, help_text="Related template")
    sms_template = models.ForeignKey(SMSTemplate, on_delete=models.CASCADE, null=True, blank=True, help_text="Related SMS template")
    
    # Additional flags (following existing pattern)
    is_co_borrower = models.BooleanField(default=False, null=True, blank=True, help_text="Whether sent to co-borrower")
    
    # API response data
    api_response = models.JSONField(default=dict, null=True, blank=True, help_text="Full API response from Infobip")
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True, help_text="Record creation timestamp")
    updated_at = models.DateTimeField(auto_now=True, help_text="Record last update timestamp")
    
    def __str__(self):
        return f"SMS to {self.phone_number} - {self.status}"
    
    class Meta:
        verbose_name = "SMS Message"
        verbose_name_plural = "SMS Messages"
        ordering = ['-created_at']


class SMSDeliveryReport(models.Model):
    """
    Model to store SMS delivery reports from Infobip webhooks.
    Tracks detailed delivery status updates.
    """
    # Message identification
    message_id = models.CharField(max_length=120, help_text="Infobip message ID")
    phone_number = models.CharField(max_length=20, help_text="Recipient phone number")
    
    # Delivery status
    status = models.CharField(max_length=50, help_text="Delivery status")
    status_description = models.TextField(null=True, blank=True, help_text="Detailed status description")
    error_code = models.CharField(max_length=20, null=True, blank=True, help_text="Error code if failed")
    error_description = models.TextField(null=True, blank=True, help_text="Error description if failed")
    
    # Timestamps from Infobip
    sent_at = models.DateTimeField(null=True, blank=True, help_text="When message was sent")
    done_at = models.DateTimeField(null=True, blank=True, help_text="When delivery was completed")
    
    # Pricing information
    price_amount = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True, help_text="Message cost")
    price_currency = models.CharField(max_length=3, null=True, blank=True, help_text="Currency code")
    
    # Raw webhook data
    webhook_data = models.JSONField(default=dict, null=True, blank=True, help_text="Full webhook payload")
    
    # Relationship to SMS message
    sms_message = models.ForeignKey(SMSMessage, on_delete=models.CASCADE, null=True, blank=True, help_text="Related SMS message")
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True, help_text="Report creation timestamp")
    updated_at = models.DateTimeField(auto_now=True, help_text="Report last update timestamp")
    
    def __str__(self):
        return f"Delivery Report: {self.message_id} - {self.status}"
    
    class Meta:
        verbose_name = "SMS Delivery Report"
        verbose_name_plural = "SMS Delivery Reports"
        ordering = ['-created_at']


class SMSCampaign(models.Model):
    """
    Model to track SMS campaigns.
    Similar to existing Campaign model but focused on SMS.
    """
    # Campaign identification
    name = models.CharField(max_length=255, help_text="Campaign name")
    description = models.TextField(null=True, blank=True, help_text="Campaign description")
    
    # Campaign configuration
    sms_template = models.ForeignKey(SMSTemplate, on_delete=models.CASCADE, help_text="SMS template used")
    sender_id = models.CharField(max_length=20, null=True, blank=True, help_text="Sender ID for campaign")
    
    # Campaign status
    status = models.CharField(
        max_length=20,
        choices=[
            ('DRAFT', 'Draft'),
            ('SCHEDULED', 'Scheduled'),
            ('RUNNING', 'Running'),
            ('COMPLETED', 'Completed'),
            ('FAILED', 'Failed'),
            ('CANCELLED', 'Cancelled'),
        ],
        default='DRAFT',
        help_text="Campaign status"
    )
    
    # Campaign statistics
    total_recipients = models.IntegerField(default=0, help_text="Total number of recipients")
    messages_sent = models.IntegerField(default=0, help_text="Number of messages sent")
    messages_delivered = models.IntegerField(default=0, help_text="Number of messages delivered")
    messages_failed = models.IntegerField(default=0, help_text="Number of messages failed")
    
    # Scheduling
    scheduled_at = models.DateTimeField(null=True, blank=True, help_text="When campaign is scheduled to run")
    started_at = models.DateTimeField(null=True, blank=True, help_text="When campaign started")
    completed_at = models.DateTimeField(null=True, blank=True, help_text="When campaign completed")
    
    # Error tracking
    errors = models.JSONField(default=list, null=True, blank=True, help_text="Campaign errors")
    
    # Relationships
    created_by = models.ForeignKey(User, on_delete=models.RESTRICT, help_text="User who created the campaign")
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True, help_text="Campaign creation timestamp")
    updated_at = models.DateTimeField(auto_now=True, help_text="Campaign last update timestamp")
    
    def __str__(self):
        return f"{self.name} - {self.status}"
    
    def get_success_rate(self):
        """Calculate campaign success rate."""
        if self.messages_sent == 0:
            return 0
        return (self.messages_delivered / self.messages_sent) * 100
    
    class Meta:
        verbose_name = "SMS Campaign"
        verbose_name_plural = "SMS Campaigns"
        ordering = ['-created_at']
