# Generated by Django 3.2 on 2025-06-18 09:08

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('odr', '0024_auto_20250520_1243'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='dispute',
            name='action_taken_respondent',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='claimant_status',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='doc',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='rv_status',
        ),
        migrations.AddField(
            model_name='dispute',
            name='claimant',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='claimant', to='odr.profile'),
        ),
        migrations.AddField(
            model_name='dispute',
            name='summary',
            field=models.CharField(default=' ', max_length=5000),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='dispute',
            name='client',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='client', to='odr.profile'),
        ),
        migrations.AlterField(
            model_name='dispute',
            name='description',
            field=models.CharField(max_length=10000),
        ),
    ]
