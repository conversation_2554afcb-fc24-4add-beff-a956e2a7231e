"""
SMS Service URLs

URL configuration for SMS service endpoints.

Compatible with:
- Python 3.9.0
- Django 3.2
- Django REST Framework 3.14.0

Author: BFSI Backend Team
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from .views import (
    SendSMSView,
    SendBulkSMSView,
    SMSStatusView,
    SendSMSTemplateView,
    UnifiedMessageSendView,
    UnifiedCampaignView,
    DisputeNotificationView,
    UnifiedQuickTestView
)

# Create router for viewsets (if needed in future)
router = DefaultRouter()

# Infobip SMS API endpoints (mimicking WhatsApp structure)
urlpatterns = [
    # Main SMS endpoints (backward compatibility)
    path('infobip/sms/send', SendSMSView.as_view(), name='infobip_send_sms'),
    path('infobip/sms/send-template', SendSMSTemplateView.as_view(), name='infobip_send_sms_template'),
    path('infobip/sms/send-bulk', SendBulkSMSView.as_view(), name='infobip_send_bulk_sms'),
    path('infobip/sms/status', SMSStatusView.as_view(), name='infobip_sms_status'),

    # Unified messaging endpoints (no database migrations required)
    path('unified/message/send', UnifiedMessageSendView.as_view(), name='unified_message_send'),
    path('unified/campaign/process', UnifiedCampaignView.as_view(), name='unified_campaign_process'),
    path('unified/dispute/notify', DisputeNotificationView.as_view(), name='unified_dispute_notify'),
    path('unified/test', UnifiedQuickTestView.as_view(), name='unified_test'),

    # Include router URLs (for future viewsets)
    path('', include(router.urls)),
]

# URL patterns with 'api/' prefix for main project
api_urlpatterns = [
    path('api/', include(urlpatterns)),
]
