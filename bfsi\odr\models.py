from django.db import models
from django.utils import timezone
from django_extensions.db.models import TimeStampedModel
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

from notice.models import Campaign
from odr.timezones import ALL_TIMEZONES
from authorization.models import User

from enum import Enum

AuthType = Enum('AuthType', 'local google')
ProfileType = Enum('ProfileType',
                   'general arbitrator conciliator admin sub_admin case_manager client sub_client claimant manager')
Gender = Enum("Gender", 'male female transgender')
ArbitratorStatus = Enum('ArbitratorStatus', 'agree webnyay_upload')
AwardType = Enum(
    'AwardType', 'partial_award final_award submission_report arbitration_petition msme_certificate')
ClaimantStatus = Enum('ClaimantStatus', 'new claimant_complete closed')
ClosedReason = Enum('ClosedReason', 'unsuccessful successful withdraw')
FlowType = Enum(
    'FlowType', 'conciliation arbitration')
DisputeLevelStatus = Enum('DisputeLevelStatus', 'one two')
MuthootStatus = Enum(
    'MuthootStatus', 'GRO_Accept GRO_Accept_nodal Forward Forward_nodal Forward_MD IO_Reject MD_GRO MD_IO Resolved GRO_After_Review')
RespondentStatus = Enum(
    'RespondentStatus', 'respondent_inprogress respondent_complete closed')
DisputeRvStatus = Enum('DisputeRvStatus',
                       'new claimant_lawyer_complete respondent_complete respondent_lawyer_complete arbitrator_complete closed')
PaymentStatus = Enum("PaymentStatus", 'incomplete processing complete')
DisputeStatus = Enum('DisputeStatus',
                     'new respondent_inprogress respondent_complete claimant_complete terms_complete price_agreement_complete professional_upload webnyay_upload award_upload complete paid closed io_inprogress io_complete md_inprogress md_complete final_respondent_inprogress final_respondent_complete')
CaseStatus = Enum('CaseStatus',
                  'new in_progress dispute_lodged questions_complete  upload_complete review_complete complete')
CaseFileType = Enum("CaseFileType", 'claimant respondent')



AuthTypeChoices = (
    (AuthType.local.name, 'local'),
    (AuthType.google.name, 'google'),

)

ProfileTypeChoices = (
    (ProfileType.general.name, 'general'),
    (ProfileType.arbitrator.name, 'arbitrator'),
    (ProfileType.conciliator.name, 'conciliator'),
    (ProfileType.admin.name, 'admin'),
    (ProfileType.sub_admin.name, 'sub_admin'),
    (ProfileType.case_manager.name, 'case_manager'),
    (ProfileType.client.name, 'client'),
    (ProfileType.sub_client.name, 'sub_client'),
    (ProfileType.claimant.name, 'claimant'),
    (ProfileType.manager.name, 'manager'),

)

GenderChoices = (
    (Gender.male.name, 'male'),
    (Gender.female.name, 'female'),
    (Gender.transgender.name, 'transgender'),
)

ArbitratorStatusChoices = (
    (ArbitratorStatus.agree.name, 'agree'),
    (ArbitratorStatus.webnyay_upload.name, 'webnyay_upload'),
)

AwardTypeChoices = (
    (AwardType.partial_award.name, 'partial_award'),
    (AwardType.final_award.name, 'final_award'),
    (AwardType.submission_report.name, 'submission_report'),
    (AwardType.arbitration_petition.name, 'arbitration_petition'),
    (AwardType.msme_certificate.name, 'msme_certificate'),
)

ClaimantStatusChoices = (
    (ClaimantStatus.new.name, 'new'),
    (ClaimantStatus.claimant_complete.name, 'claimant_complete'),
    (ClaimantStatus.closed.name, 'closed'),
)


ClosedReasonChoices = (
    (ClosedReason.unsuccessful.name, 'unsuccessful'),
    (ClosedReason.successful.name, 'successful'),
    (ClosedReason.withdraw.name, 'withdraw'),
)

FlowTypeChoices = (
    (FlowType.conciliation.name, 'conciliation'),
    (FlowType.arbitration.name, 'arbitration'),
)

DisputeLevelChoices = (
    (DisputeLevelStatus.one.name, 'one'),
    (DisputeLevelStatus.two.name, 'two'),
)

RespodentStatusChoices = (
    (RespondentStatus.respondent_inprogress.name, 'respondent_inprogress'),
    (RespondentStatus.respondent_complete.name, 'respondent_complete'),
    (RespondentStatus.closed.name, 'closed'),
)

DisputeRvStatusChoices = (
    (DisputeRvStatus.new.name, 'new'),
    (DisputeRvStatus.claimant_lawyer_complete.name, 'claimant_lawyer_complete'),
    (DisputeRvStatus.respondent_complete.name, 'respondent_complete'),
    (DisputeRvStatus.respondent_lawyer_complete.name, 'respondent_lawyer_complete'),
    (DisputeRvStatus.arbitrator_complete.name, 'arbitrator_complete'),
    (DisputeRvStatus.closed.name, 'closed'),
)

PaymentStatusChoices = (
    (PaymentStatus.incomplete.name, 'incomplete'),
    (PaymentStatus.processing.name, 'processing'),
    (PaymentStatus.complete.name, 'complete'),
)

DisputeStatusChoices = (
    (DisputeStatus.new.name, 'new'),
    (DisputeStatus.respondent_inprogress.name, "respondent_inprogress"),
    (DisputeStatus.respondent_complete.name, 'respondent_complete'),
    (DisputeStatus.claimant_complete.name, 'claimant_complete'),
    (DisputeStatus.terms_complete.name, 'terms_complete'),
    (DisputeStatus.price_agreement_complete.name, 'price_agreement_complete'),
    (DisputeStatus.professional_upload.name, 'professional_upload'),
    (DisputeStatus.webnyay_upload.name, 'webnyay_upload'),
    (DisputeStatus.award_upload.name, 'award_upload'),
    (DisputeStatus.complete.name, 'complete'),
    (DisputeStatus.paid.name, 'paid'),
    (DisputeStatus.closed.name, 'closed'),
    (DisputeStatus.io_inprogress.name, 'io_inprogress'),
    (DisputeStatus.io_complete.name, 'io_complete'),
    (DisputeStatus.md_inprogress.name, 'md_inprogress'),
    (DisputeStatus.md_complete.name, 'md_complete'),
    (DisputeStatus.final_respondent_inprogress.name, 'final_respondent_inprogress'),
    (DisputeStatus.final_respondent_complete.name, 'final_respondent_complete'),
)

CaseStatusChoices = (
    (CaseStatus.new.name, 'new'),
    (CaseStatus.in_progress.name, 'in_progress'),
    (CaseStatus.dispute_lodged.name, 'dispute_lodged'),
    (CaseStatus.questions_complete.name, 'questions_complete'),
    (CaseStatus.upload_complete.name, 'upload_complete'),
    (CaseStatus.review_complete.name, 'review_complete'),
    (CaseStatus.complete.name, 'complete')
)

CaseFileTypeChoices = (
    (CaseFileType.claimant.name, 'claimant'),
    (CaseFileType.respondent.name, 'respondent'),
)

TIMEZONE_CHOICES = [(tz, tz) for tz in ALL_TIMEZONES]

class Company(TimeStampedModel):
    name = models.CharField(max_length=300)
    deleted = models.BooleanField(default=False)
    is_action_taken_implemented = models.BooleanField(default=False)
    flow_type = models.CharField(
        max_length=300, blank=True, null=True, choices=FlowTypeChoices)

    def __str__(self):
        return str(self.name)

class Profile(TimeStampedModel):
    user = models.OneToOneField(User, on_delete=models.CASCADE, unique=True)
    auth_type = models.CharField(
        max_length=300, default=AuthType.local.name, choices=AuthTypeChoices)
    phone_number = models.CharField(
        max_length=100, unique=True, null=True, default=None, blank=True)
    company = models.ForeignKey(
        Company, on_delete=models.CASCADE, null=True, blank=True)
    birth_date = models.DateField(null=True, blank=True)
    profile_type = models.CharField(
        max_length=300, default=ProfileType.general.name, choices=ProfileTypeChoices)
    gender = models.CharField(
        max_length=300, default=Gender.male.name, choices=GenderChoices)
    is_validated = models.BooleanField(default=False)
    # open_dispute_count = models.IntegerField(null=True, blank=True, default=0)
    # close_dispute_count = models.IntegerField(null=True, blank=True, default=0)
    dispute_modes = models.CharField(max_length=300, blank=True, null=True)
    address = models.CharField(max_length=1000, blank=True, null=True)
    city = models.CharField(max_length=1000, blank=True, null=True)
    state =models.CharField(max_length=1000, blank=True, null=True)
    region = models.CharField(max_length=1000, blank=True, null=True)
    postal_code = models.CharField(max_length=1000, blank=True, null=True)
    created_by = models.ForeignKey(User, on_delete=models.RESTRICT, null=True, blank=True,
                                   related_name='user')
    created_by_process = models.CharField(
        max_length=300, null=True, default=None, blank=True)
    fax = models.CharField(max_length=50, null=True, default=None, blank=True)
    vua_id = models.CharField(
        max_length=300, null=True, default=None, blank=True)
    # * vua_id is used as organiation namein difc
    identity_file = models.ForeignKey(
        "LegalDocument", on_delete=models.SET_NULL, null=True, blank=True)
    timezone = models.CharField(
        choices=TIMEZONE_CHOICES, max_length=50, null=True, blank=True)
    parent_client = models.ForeignKey('self',on_delete=models.SET_NULL,null=True,
        blank=True, related_name='claimants', limit_choices_to={'profile_type': 'client'})
    gst_number = models.CharField(max_length=50, blank=True, null=True)
    pan_number = models.CharField(max_length=50, blank=True, null=True)

    def clean(self):
        if self.timezone and self.timezone not in dict(TIMEZONE_CHOICES):
            raise ValidationError({'timezone': _('Invalid timezone choice.')})

class CompanyProduct(TimeStampedModel):
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    name = models.CharField(max_length=300)
    deleted = models.BooleanField(default=False)

class LegalDocument(TimeStampedModel):
    name = models.CharField(max_length=300)
    path = models.CharField(max_length=1000, unique=True)
    type = models.CharField(max_length=100)
    award_type = models.CharField(
        max_length=300, blank=True, null=True, choices=AwardTypeChoices)
    s3path = models.CharField(max_length=1000, null=True, blank=True)
    is_final = models.BooleanField(default=False)
    upload_by = models.ForeignKey(
        Profile, on_delete=models.RESTRICT, null=True, blank=True)
    is_rfa_rv = models.BooleanField(default=False)


class Dispute(TimeStampedModel):    
    all_docs = models.ManyToManyField(
        LegalDocument, related_name="all_docs", default=None, blank=True)    
    arbitrator_status = models.CharField(
        max_length=300, default=None, blank=True, null=True, choices=ArbitratorStatusChoices)
    case_manager_rv = models.ManyToManyField(
        Profile, related_name="case_manager_rv", default=None, blank=True)    
    closed_date = models.DateTimeField(null=True,blank=True)
    closed_reason = models.CharField(
        max_length=20, choices=ClosedReasonChoices, default=None, null=True)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, blank=True, null=True)
    created_by = models.ForeignKey(
        Profile, on_delete=models.RESTRICT, null=True, blank=True, related_name='createdby_profile')
    closed_by = models.ForeignKey(
        Profile, on_delete=models.RESTRICT, null=True, blank=True, related_name='closedby_profile')
    description = models.CharField(max_length=4000)    
    flow_type = models.CharField(
        max_length=300, blank=True, choices=FlowTypeChoices)
    name = models.CharField(max_length=300)
    order_date = models.DateField(null=True, blank=True, default=timezone.now)  # Changed from auto_now_add
    professionals = models.ManyToManyField(
        Profile, related_name="disputes", default=None, blank=True)
    region = models.CharField(max_length=300, null=True, blank=True)    
    status = models.CharField(
        max_length=300, default=DisputeStatus.new.name, choices=DisputeStatusChoices)
    loan_id = models.CharField(max_length=255, default=None, blank=True, null=True)
    campaign = models.ForeignKey(Campaign, on_delete=models.SET_NULL, null=True, blank=True)
    client = models.ForeignKey(Profile, on_delete=models.SET_NULL, null=True, blank=True,related_name='client')
    claimant = models.ForeignKey(Profile, on_delete=models.SET_NULL, null=True, blank=True,related_name='claimant')
    arbitrator_rv = models.ManyToManyField(
        Profile, related_name="arbitrator_rv", null=True, blank=True)
    arbitrator_assigned_date = models.DateTimeField(null=True, blank=True)
    case_manager_assigned_date = models.DateTimeField(null=True, blank=True)
    respondents_email = models.CharField(max_length=100, null=True, blank=True)
    respondents_name = models.CharField(max_length=100, null=True, blank=True)
    co_borrowers = models.JSONField(default=list, null=True, blank=True)

    def save(self, *args, **kwargs):
        # Ensure all datetime fields are timezone-aware UTC
        if self.arbitrator_assigned_date and timezone.is_naive(self.arbitrator_assigned_date):
            self.arbitrator_assigned_date = timezone.make_aware(self.arbitrator_assigned_date, timezone.utc)
        if self.case_manager_assigned_date and timezone.is_naive(self.case_manager_assigned_date):
            self.case_manager_assigned_date = timezone.make_aware(self.case_manager_assigned_date, timezone.utc)
        super().save(*args, **kwargs)




class Folder(TimeStampedModel):
    name = models.CharField(max_length=1000)
    uploaded_by = models.ForeignKey(
        User, on_delete=models.RESTRICT, null=True, blank=True, related_name='uploaded')
    view_permission = models.ManyToManyField(
        User, related_name="view", default=None, blank=True)
    edit_permission = models.ManyToManyField(
        User, related_name="edit", default=None, blank=True)
    dispute = models.ForeignKey(
        Dispute, on_delete=models.CASCADE, null=True, blank=True, related_name='dispute')
    parent_folder = models.ForeignKey(
        "self", on_delete=models.CASCADE, null=True, blank=True)

class File(TimeStampedModel):
    name = models.CharField(max_length=1000)
    uploaded_by = models.ForeignKey(User, on_delete=models.RESTRICT, null=True, blank=True,
                                    related_name='uploaded_by')
    view_permission = models.ManyToManyField(
        User, related_name="view_permission", default=None, blank=True)
    permission = models.ManyToManyField(
        User, related_name="edit_permission", default=None, blank=True)
    dispute = models.ForeignKey(
        Dispute, on_delete=models.CASCADE, null=True, blank=True, related_name='disputes')
    parent_folder = models.ForeignKey(
        Folder, on_delete=models.CASCADE, null=True, blank=True)
    path = models.CharField(max_length=1000)
    type = models.CharField(max_length=100)
    size = models.CharField(max_length=300)