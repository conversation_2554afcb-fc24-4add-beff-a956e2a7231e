# Generated by Django 3.2 on 2025-04-02 13:13

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('odr', '0008_auto_20250402_1648'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='company',
            name='company_type',
        ),
        migrations.RemoveField(
            model_name='company',
            name='platform_type',
        ),
        migrations.RemoveField(
            model_name='companyproduct',
            name='type',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='grievance_type',
        ),
        migrations.AddField(
            model_name='casefile',
            name='loan_id',
            field=models.CharField(blank=True, default=None, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='dispute',
            name='loan_id',
            field=models.CharField(blank=True, default=None, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='casefile',
            name='proposed_flow',
            field=models.Char<PERSON>ield(choices=[('conciliation', 'conciliation'), ('arbitration', 'arbitration')], default='arbitration', max_length=300),
        ),
        migrations.AlterField(
            model_name='casefile',
            name='type',
            field=models.CharField(choices=[('claimant', 'claimant'), ('respondent', 'respondent')], max_length=300),
        ),
        migrations.AlterField(
            model_name='company',
            name='flow_type',
            field=models.CharField(blank=True, choices=[('conciliation', 'conciliation'), ('arbitration', 'arbitration')], max_length=300, null=True),
        ),
        migrations.AlterField(
            model_name='dispute',
            name='flow_type',
            field=models.CharField(blank=True, choices=[('conciliation', 'conciliation'), ('arbitration', 'arbitration')], max_length=300),
        ),
    ]
