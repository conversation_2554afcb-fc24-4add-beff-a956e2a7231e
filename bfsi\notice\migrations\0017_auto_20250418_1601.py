# Generated by Django 3.2 on 2025-04-18 10:31

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('odr', '0016_auto_20250418_1358'),
        ('notice', '0016_merge_0013_notice_created_at_0015_auto_20250417_1358'),
    ]

    operations = [
        migrations.CreateModel(
            name='EmailTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('template_id', models.CharField(blank=True, max_length=100, null=True)),
                ('name', models.CharField(max_length=255)),
                ('subject', models.CharField(blank=True, max_length=255, null=True)),
                ('body', models.TextField(help_text='Full body of the template')),
                ('requires_attachment', models.BooleanField(default=False)),
                ('created_at', models.DateTime<PERSON>ield(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.AddField(
            model_name='campaign',
            name='assigned_arbitrators_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='campaign',
            name='assigned_case_managers_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.CreateModel(
            name='Template',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('template_id', models.CharField(max_length=100, unique=True)),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('campaign', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, to='notice.campaign')),
                ('email_template', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='parent_template', to='notice.emailtemplate')),
                ('profile', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='templates', to='odr.profile')),
                ('whatsapp_template', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='parent_template', to='notice.whatsapptemplate')),
            ],
        ),
        migrations.AddField(
            model_name='emailtemplate',
            name='campaign',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, to='notice.campaign'),
        ),
        migrations.AddField(
            model_name='emailtemplate',
            name='profile',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='email_templates', to='odr.profile'),
        ),
    ]
