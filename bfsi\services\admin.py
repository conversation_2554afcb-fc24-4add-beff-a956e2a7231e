"""
Admin configuration for SMS models.
Follows the same patterns as existing admin configurations.
"""

from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe

from .models import SMSTemplate, SMSMessage, SMSDeliveryReport, SMSCampaign


@admin.register(SMSTemplate)
class SMSTemplateAdmin(admin.ModelAdmin):
    """Admin interface for SMS Templates."""
    
    list_display = (
        'name', 
        'sms_template_id', 
        'sender_id',
        'is_active', 
        'sms_messages_sent',
        'sms_status',
        'created_at'
    )
    
    list_filter = (
        'is_active',
        'sms_status',
        'is_s21_notice',
        'is_s138_notice',
        'is_termination_notice',
        'created_at'
    )
    
    search_fields = (
        'name',
        'sms_template_id',
        'body',
        'sender_id'
    )
    
    readonly_fields = (
        'created_at',
        'updated_at',
        'sms_messages_sent',
        'sms_processed_rows'
    )
    
    fieldsets = (
        ('Template Information', {
            'fields': (
                'name',
                'sms_template_id',
                'body',
                'sender_id',
                'is_active'
            )
        }),
        ('Configuration', {
            'fields': (
                'requires_attachment',
                'has_body_params',
                'parameter_mapping'
            )
        }),
        ('Notice Types', {
            'fields': (
                'is_s21_notice',
                'is_s138_notice',
                'is_termination_notice',
                'is_payment_request_notice'
            )
        }),
        ('Conciliation Notices', {
            'fields': (
                'conciliation_notice_1',
                'conciliation_notice_2',
                'conciliation_notice_3',
                'conciliation_notice_4'
            ),
            'classes': ('collapse',)
        }),
        ('SMS Status', {
            'fields': (
                'sent_to_sms',
                'sms_processed_rows',
                'sms_messages_sent',
                'sms_status',
                'sms_errors'
            ),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': (
                'created_by',
                'created_at',
                'updated_at'
            ),
            'classes': ('collapse',)
        })
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('created_by')


@admin.register(SMSMessage)
class SMSMessageAdmin(admin.ModelAdmin):
    """Admin interface for SMS Messages."""
    
    list_display = (
        'phone_number',
        'message_id',
        'status',
        'sender_id',
        'dispute_link',
        'template_link',
        'sent_timestamp',
        'created_at'
    )
    
    list_filter = (
        'status',
        'sender_id',
        'is_co_borrower',
        'sent_timestamp',
        'created_at'
    )
    
    search_fields = (
        'phone_number',
        'message_id',
        'message_text',
        'dispute__id',
        'template__name'
    )
    
    readonly_fields = (
        'message_id',
        'api_response',
        'created_at',
        'updated_at'
    )
    
    fieldsets = (
        ('Message Information', {
            'fields': (
                'phone_number',
                'message_id',
                'message_text',
                'sender_id'
            )
        }),
        ('Status', {
            'fields': (
                'status',
                'status_description',
                'sent_timestamp',
                'delivered_timestamp',
                'failed_timestamp'
            )
        }),
        ('Relationships', {
            'fields': (
                'dispute',
                'template',
                'sms_template',
                'is_co_borrower'
            )
        }),
        ('API Data', {
            'fields': (
                'api_response',
            ),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': (
                'created_at',
                'updated_at'
            ),
            'classes': ('collapse',)
        })
    )
    
    def dispute_link(self, obj):
        """Create a link to the related dispute."""
        if obj.dispute:
            url = reverse('admin:odr_dispute_change', args=[obj.dispute.id])
            return format_html('<a href="{}">{}</a>', url, obj.dispute.id)
        return '-'
    dispute_link.short_description = 'Dispute'
    
    def template_link(self, obj):
        """Create a link to the related template."""
        if obj.template:
            url = reverse('admin:notice_template_change', args=[obj.template.id])
            return format_html('<a href="{}">{}</a>', url, obj.template.name)
        return '-'
    template_link.short_description = 'Template'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'dispute', 'template', 'sms_template'
        )


@admin.register(SMSDeliveryReport)
class SMSDeliveryReportAdmin(admin.ModelAdmin):
    """Admin interface for SMS Delivery Reports."""
    
    list_display = (
        'message_id',
        'phone_number',
        'status',
        'error_code',
        'price_display',
        'sent_at',
        'done_at',
        'created_at'
    )
    
    list_filter = (
        'status',
        'error_code',
        'price_currency',
        'sent_at',
        'done_at',
        'created_at'
    )
    
    search_fields = (
        'message_id',
        'phone_number',
        'status',
        'error_code',
        'error_description'
    )
    
    readonly_fields = (
        'message_id',
        'webhook_data',
        'created_at',
        'updated_at'
    )
    
    fieldsets = (
        ('Message Information', {
            'fields': (
                'message_id',
                'phone_number',
                'sms_message'
            )
        }),
        ('Delivery Status', {
            'fields': (
                'status',
                'status_description',
                'error_code',
                'error_description'
            )
        }),
        ('Timing', {
            'fields': (
                'sent_at',
                'done_at'
            )
        }),
        ('Pricing', {
            'fields': (
                'price_amount',
                'price_currency'
            )
        }),
        ('Raw Data', {
            'fields': (
                'webhook_data',
            ),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': (
                'created_at',
                'updated_at'
            ),
            'classes': ('collapse',)
        })
    )
    
    def price_display(self, obj):
        """Display price with currency."""
        if obj.price_amount and obj.price_currency:
            return f"{obj.price_amount} {obj.price_currency}"
        return '-'
    price_display.short_description = 'Price'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('sms_message')


@admin.register(SMSCampaign)
class SMSCampaignAdmin(admin.ModelAdmin):
    """Admin interface for SMS Campaigns."""
    
    list_display = (
        'name',
        'status',
        'sms_template',
        'total_recipients',
        'messages_sent',
        'success_rate_display',
        'created_by',
        'created_at'
    )
    
    list_filter = (
        'status',
        'sender_id',
        'scheduled_at',
        'started_at',
        'completed_at',
        'created_at'
    )
    
    search_fields = (
        'name',
        'description',
        'sms_template__name',
        'created_by__email'
    )
    
    readonly_fields = (
        'messages_sent',
        'messages_delivered',
        'messages_failed',
        'started_at',
        'completed_at',
        'created_at',
        'updated_at',
        'success_rate_display'
    )
    
    fieldsets = (
        ('Campaign Information', {
            'fields': (
                'name',
                'description',
                'sms_template',
                'sender_id'
            )
        }),
        ('Status & Scheduling', {
            'fields': (
                'status',
                'scheduled_at',
                'started_at',
                'completed_at'
            )
        }),
        ('Statistics', {
            'fields': (
                'total_recipients',
                'messages_sent',
                'messages_delivered',
                'messages_failed',
                'success_rate_display'
            )
        }),
        ('Errors', {
            'fields': (
                'errors',
            ),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': (
                'created_by',
                'created_at',
                'updated_at'
            ),
            'classes': ('collapse',)
        })
    )
    
    def success_rate_display(self, obj):
        """Display success rate as percentage."""
        rate = obj.get_success_rate()
        if rate > 0:
            color = 'green' if rate >= 90 else 'orange' if rate >= 70 else 'red'
            return format_html(
                '<span style="color: {};">{:.1f}%</span>',
                color,
                rate
            )
        return '-'
    success_rate_display.short_description = 'Success Rate'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'sms_template', 'created_by'
        )
