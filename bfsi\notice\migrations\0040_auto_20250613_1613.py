# Generated by Django 3.2 on 2025-06-13 10:43

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('notice', '0039_auto_20250610_1627'),
    ]

    operations = [
        migrations.RenameField(
            model_name='emailtemplate',
            old_name='camp_invite_notice',
            new_name='conciliation_notice_1',
        ),
        migrations.RenameField(
            model_name='whatsapptemplate',
            old_name='camp_invite_notice',
            new_name='conciliation_notice_1',
        ),
        migrations.AddField(
            model_name='emailtemplate',
            name='conciliation_notice_4',
            field=models.BooleanField(blank=True, default=False, null=True),
        ),
        migrations.AddField(
            model_name='whatsapptemplate',
            name='conciliation_notice_2',
            field=models.BooleanField(blank=True, default=False, null=True),
        ),
        migrations.AddField(
            model_name='whatsapptemplate',
            name='conciliation_notice_3',
            field=models.<PERSON><PERSON>anField(blank=True, default=False, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name='whatsapptemplate',
            name='conciliation_notice_4',
            field=models.<PERSON>oleanField(blank=True, default=False, null=True),
        ),
    ]
