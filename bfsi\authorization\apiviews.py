
import random
import re
import uuid
from datetime import timed<PERSON>ta

import secrets
import tldextract
from django.conf import settings
from django.contrib.auth.hashers import make_password
from django.contrib.auth.models import update_last_login
from django.contrib.auth.password_validation import (get_password_validators,
                                                     validate_password)
from django.core.cache import cache
from django.core.exceptions import ValidationError
from django.http import JsonResponse
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django_rest_passwordreset.models import (
    ResetPasswordToken, get_password_reset_token_expiry_time)
from django_rest_passwordreset.serializers import PasswordTokenSerializer
from django_rest_passwordreset.signals import (post_password_reset,
                                               pre_password_reset)
from firebase_admin import auth
from rest_framework import exceptions, generics, serializers, status, views
from rest_framework.decorators import api_view
from rest_framework.generics import GenericAPIView, get_object_or_404
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework.views import APIView
from authorization.models import OTP, EmailOtp, User, state_city_map
from authorization.serializers import ChangePasswordSerializer, UserSerializer, ResetPasswordSerializer, PasswordTokenSerializer
from odr.mailhelpers import (sendOtpEmail, sendResetPassword)
from odr.models import Profile
from odr.utils import add_dispute_mode_to_profile, getPlatformLinkByUrl
from odr.helperfunctions.databasefunctions import get_database_name


@api_view(['GET'])
def current_user(request):
    """
    Determine the current user by their token, and return their data
    """

    serializer = UserSerializer(request.user)
    return Response(serializer.data)


class ChangePassword(views.APIView):
    def patch(self, request):
        serializer = ChangePasswordSerializer
        user = self.request.user
        if user.is_anonymous:
            return JsonResponse(data={"error": "user not authenticated"}, status=401)

        instance = serializer(data=request.data)
        if instance.is_valid():

            if user.check_password(instance.data["currentPassword"]):
                user.set_password(instance.data['newPassword'])
                user.save()
                return JsonResponse(data={"success": "Password changed"}, status=200)
            else:
                return JsonResponse(data={"error": "wrong password"}, status=400)
        else:

            return JsonResponse(data={"error": instance._errors}, status=400)


def create_user_if_not(email, first_name, last_name, password):
    query_user = User.objects.filter(email=email)

    if query_user.exists():
        return query_user.get()
    else:
        return User.objects.create(email=email, first_name=first_name, last_name=last_name, password=make_password(password))


def create_profile_if_not(user, auth_type, profile_type):
    query_profile = Profile.objects.filter(user=user)
    if query_profile.exists():
        return query_profile.get()
    else:
        profile = Profile.objects.create(
            user=user, auth_type=auth_type, profile_type=profile_type)
        profile.created_by_process = 'create_profile_if_not'
        profile.save()
        return profile


class CustomResetPasswordConfirm(GenericAPIView):
    """
    An Api View which provides a method to reset a password based on a unique token
    """
    throttle_classes = ()
    permission_classes = ()
    serializer_class = PasswordTokenSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        email = request.data['email']
        password = serializer.validated_data['password']
        token = serializer.validated_data['token']
        user = get_object_or_404(User, email=email)

        # get token validation time
        password_reset_token_validation_time = get_password_reset_token_expiry_time()

        # find token
        reset_password_token = ResetPasswordToken.objects.filter(
            key=token).first()

        if reset_password_token is None:
            return Response({'status': 'notfound'}, status=status.HTTP_404_NOT_FOUND)

        # check expiry date
        expiry_date = reset_password_token.created_at + \
            timedelta(hours=password_reset_token_validation_time)

        if timezone.now() > expiry_date:
            # delete expired token
            reset_password_token.delete()
            return Response({'status': 'expired'}, status=status.HTTP_404_NOT_FOUND)

        # change users password (if we got to this code it means that the user is_active)
        if reset_password_token.user.eligible_for_reset():
            pre_password_reset.send(
                sender=self.__class__, user=reset_password_token.user)
            try:
                # validate the password against existing validators
                validate_password(
                    password,
                    user=reset_password_token.user,
                    password_validators=get_password_validators(
                        settings.AUTH_PASSWORD_VALIDATORS)
                )
            except ValidationError as e:
                # raise a validation error for the serializer
                raise exceptions.ValidationError({
                    'password': e.messages
                })

            user.set_password(password)
            user.save()
            post_password_reset.send(
                sender=self.__class__, user=reset_password_token.user)

        # Delete all password reset tokens for this user
        ResetPasswordToken.objects.filter(
            user=reset_password_token.user).delete()

        return Response({'status': 'OK'})


class GetOtpEmail(views.APIView):

    permission_classes = [AllowAny]

    def post(self, request):
        host = self.request.query_params.get('host', '')
        data = request.data
        regex = '^(\w|\.|\_|\-)+[@](\w|\_|\-|\.)+[.]\w{2,3}$'
        email = ''
        name = ''
        mobile = ''
        if 'email' not in data.keys():
            return JsonResponse(data={"error": "Please provide the correct email-id"},
                                status=status.HTTP_400_BAD_REQUEST)
        if 'mobile' not in data.keys():
            return JsonResponse(data={"error": "Please provide the correct mobile number"},
                                status=status.HTTP_400_BAD_REQUEST)
        elif 'name' not in data.keys():
            return JsonResponse(data={"error": "Please provide the name"},
                                status=status.HTTP_400_BAD_REQUEST)
        else:
            email = data['email']
            name = data['name']
            mobile = data['mobile']
        if not (re.search(regex, email)):
            return JsonResponse(data={"error": "Please provide the correct email-id"},
                                status=status.HTTP_400_BAD_REQUEST)
        # if not (re.search(regex_mobile, mobile)):
        #     return JsonResponse(data={"error": "Please provide the correct mobile number"},
        #                         status=status.HTTP_400_BAD_REQUEST)
        if name == '':
            return JsonResponse(data={"error": "Please provide the name"},
                                status=status.HTTP_400_BAD_REQUEST)
        if mobile == '' and host not in ['hotstar', 'star', 'muthoot']:
            return JsonResponse(data={"error": "Please provide the mobile number"},
                                status=status.HTTP_400_BAD_REQUEST)
        error = {}
        if User.objects.filter(email=email).first():
            error["user"] = {
                "email": ["user with this email address already exists."]}
            error['status'] = 400
        if mobile != '' and mobile and Profile.objects.filter(phone_number=mobile).first():
            error['phone_number'] = [
                "profile with this phone number already exists."]
            error['status'] = 400
        if error:
            raise serializers.ValidationError(error)

        number = ''.join([str(random.randint(0, 9)) for _ in range(4)])
        time_delta = timedelta(minutes=5)

        email_otp = EmailOtp()
        email_otp.email = email
        email_otp.name = name
        email_otp.otp = number
        email_otp.save()
        expiry_date = email_otp.date_created + time_delta
        email_otp.date_expired = expiry_date
        email_otp.save()
        print("expiry date", expiry_date)
        sendOtpEmail(otp=number, email=email, name=name)
        return JsonResponse(data={"success": "Otp sent"}, status=status.HTTP_200_OK)


class CustomPasswordResetView(views.APIView):
    authentication_classes = []
    permission_classes = []

    def post(self, request):
        host = request.META.get('HTTP_REFERER', None)
        # host='https://dev-app.webnyay.in/'

        if get_database_name() == 'backend-bfsi-dev':
            frontend = "https://bfsi-dev-app.webnyay.in/"
        elif get_database_name() == "backend-bfsi-prod":
            frontend = "https://bfsi-app.webnyay.in/" 
        user = User.objects.filter(email=self.request.data['email']).first()

        if not user:
            return JsonResponse({'status': 'not OK', 'reason': "No user found"})
        first_name = user.first_name

        token = ResetPasswordToken.objects.create(user_id=user.id)

        token_url = f'{frontend}reset-password?token={token.key}&email={user.email}'

        sendResetPassword(email=user.email, token_url=token_url)
        return JsonResponse({'status': "OK"})


class ResetPasswordView(generics.UpdateAPIView):
    serializer_class = ResetPasswordSerializer
    # To Do ------- Only allow admin users to delete other users

    def update(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        user_id = serializer.validated_data['user_id']
        new_password = serializer.validated_data['new_password']

        user = User.objects.get(pk=user_id)
        user.set_password(new_password)
        user.save()

        return Response({"message": "Password has been reset successfully."})


class CityByStateView(APIView):
    permission_classes = [AllowAny]

    def get(self, request, state):
        cities = state_city_map.get(state)

        if cities:
            return Response(cities, status=200)
        else:
            return Response({"error": "State not found"})
