# consumers.py
from channels.generic.websocket import AsyncWebsocketConsumer

class CampaignProgressConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.campaign_id = self.scope['url_route']['kwargs']['campaign_id']
        await self.accept()
        
        while True:
            campaign = await sync_to_async(Campaign.objects.get)(id=self.campaign_id)
            await self.send(json.dumps(campaign.get_progress()))
            await asyncio.sleep(2)  # Update every 2 seconds
