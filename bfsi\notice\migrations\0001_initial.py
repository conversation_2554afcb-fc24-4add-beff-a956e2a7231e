# Generated by Django 3.2 on 2025-03-05 06:26

from django.db import migrations, models
import django.db.models.deletion
import django_extensions.db.fields


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Company',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name='created')),
                ('modified', django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name='modified')),
                ('name', models.CharField(max_length=300)),
                ('json_data', models.TextField(blank=True, default='{}', null=True)),
            ],
            options={
                'get_latest_by': 'modified',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name='created')),
                ('modified', django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name='modified')),
                ('name', models.CharField(blank=True, max_length=300, null=True)),
                ('phone_number', models.CharField(blank=True, default=None, max_length=100, null=True, unique=True)),
                ('meeting_link', models.CharField(blank=True, max_length=300, null=True)),
                ('json_data', models.TextField(blank=True, default='{}', null=True)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='notice.company')),
            ],
            options={
                'get_latest_by': 'modified',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Entry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('msg_id', models.CharField(max_length=100)),
                ('phone_number', models.CharField(max_length=15)),
                ('status', models.CharField(choices=[('initiated', 'Initiated'), ('sent', 'Sent'), ('delivered', 'Delivered'), ('read', 'Read'), ('failed', 'Failed')], max_length=10)),
                ('failed_reason', models.CharField(blank=True, default='', max_length=100, null=True)),
                ('timestamp', models.DateTimeField()),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='notice.user')),
            ],
        ),
    ]
