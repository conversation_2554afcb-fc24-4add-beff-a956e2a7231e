# from odr.helperfunctions.databasefunctions import is_hotstar_database, is_production_database
# from odr.helperfunctions.gcs import gcs_manager
# from odr.helperfunctions.sendgridfunctions import (
#     send_email_template,
#     sendTemplateEmailDIFC)
# # from odr.utils import (GenerateDocEmailHelper, get_forgot_password_link, getPlatformLinkByHost,
# #                             getPlatformLinkByUrl)
# from bfsi.settings import MAIL_ID
from bfsi.settings import S21_MAIL_ID, MAIL_ID
from odr.helperfunctions.sendgridfunctions import send_email_template, sendAttchmentEmailTiac, sendTemplateEmail, is_production_database


# def sendProfileCreatedRV(email="", password="", group=""):
#     template_id = "d-31f6abf5a46b41e3bfca28b74848a0b4"
#     if group == 'arb':
#         link = 'https://arb.webnyay.in/'
#     elif group == 'kilumaya':
#         link = 'https://kilumaya.webnyay.ai/'
#     else:
#         link = 'https://resolvevirtually.com/'
#     forget_link = get_forgot_password_link(link)
#     return sendTemplateEmail(to=email,
#                             template_id=template_id,
#                             substition_data={
#                                 'Email_ID': email,
#                                 'temp_password': password,
#                                 'here': link,
#                                 'link': forget_link
#                             })

# def sendDisputeCreated(dispute, email="", name=""):
#     template_id = "d-9c332158ce6049faa3acc302faeb967f"
#     return sendTemplateEmail(to=email, template_id=template_id, subject="Dispute created" +
#                             dispute.name, substition_data={
#                                 "first_name": name,
#                                 "dispute_name": dispute.name,
#                                 "dispute_link": getPlatformLinkByUrl()
#                             })


# def sendDisputeCreatedClaiment(dispute, email="", r_f_name="", r_l_name="", c_f_name="", c_l_name="", host="", h='', dispute_id_for_custom_args=""):
#     template_id = "d-9c332158ce6049faa3acc302faeb967f"
#     digipub_template_id = "d-00f162e64261415da178891d228b6f4b"
#     template_id_newsclick_cliamant = "d-48333c420c544ba6aa79f5e94c7a2f65"
#     template_id_rv = 'd-1b52bb1c9d5e43478cfdf1f657ee8c2c'
#     custom_args = {"dispute_id": dispute_id_for_custom_args}
#     if (h == 'digipub'):
#         if dispute.company_product.company.name == 'NewsClick':
#             return sendTemplateEmailNewsClick(to=email,
#                                             template_id=template_id_newsclick_cliamant,
#                                             substition_data={
#                                                 "first_name": c_f_name,
#                                                 "company_name": dispute.company_product.company.name,
#                                                 "grievance_ID": dispute.id,
#                                                 "title_name": dispute.name,
#                                                 "link": getPlatformLinkByHost(h)
#                                             },
#                                             custom_args=custom_args)
#         else:
#             return sendTemplateEmail(to=email,
#                                     template_id=digipub_template_id,
#                                     substition_data={
#                                         "first_name": c_f_name,
#                                         "respondent_name": r_f_name+" "+r_l_name,
#                                         "respondent_company_name": dispute.company_product.company.name,
#                                         "grievance_ID": dispute.id,
#                                         "title_name": dispute.name,
#                                         "link": getPlatformLinkByHost(h)
#                                     },
#                                     custom_args=custom_args)
#     elif h == 'rv':
#         return sendTemplateEmail(to=email,
#                                 template_id=template_id_rv,
#                                 substition_data={
#                                     "first_name": c_f_name,
#                                     "respondent_name": r_f_name+" "+r_l_name,
#                                     "respondent_company_name": dispute.name,
#                                     "grievance_ID": dispute.id,
#                                     "title_name": dispute.name,
#                                     "link": getPlatformLinkByHost(h)
#                                 },
#                                 custom_args=custom_args)
#     else:
#         return sendTemplateEmail(to=email,
#                                 template_id=template_id,
#                                 substition_data={
#                                     "first_name": c_f_name,
#                                     "respondent_name": r_f_name + " " + r_l_name,
#                                     "grievance_ID": dispute.id,
#                                     "title_name": dispute.name,
#                                     "link": getPlatformLinkByHost(h)
#                                 },
#                                 custom_args=custom_args)


# def sendDisputeCreatedRespondant(dispute, email="", r_f_name="", r_l_name="", c_f_name="", c_l_name="", host="", h='', dispute_id_for_custom_args=""):
#     template_id = "d-9c332158ce6049faa3acc302faeb967f"
#     template_id_newsclick = "d-6f55d47bd5e2465f8ef83e6b4a3fad67"
#     template_id_rv = 'd-1b52bb1c9d5e43478cfdf1f657ee8c2c'
#     custom_args = {"dispute_id": dispute_id_for_custom_args}
#     if h == 'digipub' and dispute.company_product.company.name == 'NewsClick':
#         return sendTemplateEmailNewsClick(to=email,
#                                         template_id=template_id_newsclick,
#                                         substition_data={
#                                             "first_name": r_f_name,
#                                             "company_name": dispute.company_product.company.name,
#                                             "complainant_name": c_f_name,
#                                             "grievance_ID": dispute.id,
#                                             "title_name": dispute.name,
#                                             "link": getPlatformLinkByHost(h)
#                                         },
#                                         custom_args=custom_args)
#     elif h == 'rv':
#         return sendTemplateEmail(to=email,
#                                 template_id=template_id_rv,
#                                 substition_data={
#                                     "first_name": r_f_name,
#                                     "respondent_name": c_f_name+" "+c_l_name,
#                                     "respondent_company_name": dispute.name,
#                                     "grievance_ID": dispute.id,
#                                     "title_name": dispute.name,
#                                     "link": getPlatformLinkByHost(h)
#                                 },
#                                 custom_args=custom_args)
#     else:
#         return sendTemplateEmail(to=email,
#                                 template_id=template_id,
#                                 substition_data={
#                                     "first_name": r_f_name,
#                                     "respondent_name": r_f_name+" "+r_l_name,
#                                     "grievance_ID": dispute.id,
#                                     "title_name": dispute.name,
#                                     "link": getPlatformLinkByHost(h)
#                                 },
#                                 custom_args=custom_args)


# def sendDisputeCreatedToAdmin(dispute, email, claimant_name, claimant_email, respondent_name, respondent_email, claimant_number, respondent_number, date, dispute_id, dispute_id_for_custom_args=""):
#     template_id = "d-0666210d36854fa68babf39994f47b0c"
#     custom_args = {"dispute_id": dispute_id_for_custom_args}
#     return sendTemplateEmail(to=email,
#                             template_id=template_id,
#                             substition_data={
#                                 "claimant_name": claimant_name,
#                                 "respondent_name": respondent_name,
#                                 "respondent_email": respondent_email,
#                                 "respondent_number": respondent_number,
#                                 "claimant_email": claimant_email,
#                                 "claimant_number": claimant_number,
#                                 "date": date,
#                                 "dispute_ID": dispute_id
#                             },
#                             custom_args=custom_args)


def sendResetPassword(email, token_url):
    if is_production_database():
        cced_emails = ["<EMAIL>"]
    else:
        cced_emails = ["<EMAIL>"]
    template_id = "d-05c239eb9d1a46c5a8163259d2ee22e3"
    return sendTemplateEmail(to=email,
                            template_id=template_id,
                            substition_data={
                                "token_url": token_url
                            },
                            cc_emails=cced_emails)


# def sendResetPasswordRV(email, token_url):
#     template_id = "d-f13018261a294eeaa22c265c986e0889"
#     return sendTemplateEmail(to=email,
#                             template_id=template_id,
#                             substition_data={
#                                 "token_url": token_url
#                             })


# def sendResetPasswordHotstar(email, token_url, first_name):
#     template_id = "d-b093588888f44628b542252abd9f9aaa"
#     sendTemplateEmail(to=email,
#                     template_id=template_id,
#                     substition_data={
#                         "first_name": first_name,
#                         "token_url": token_url
#                     })

# def sendResetPasswordMuthoot(email, token_url):
#     template_id = "d-0588de1de1994b4b9d075da7f711ee1a"
#     sendTemplateEmail(to=email,
#                     template_id=template_id,
#                     substition_data={
#                         "token_url": token_url
#                     })


# def sendResetPasswordDifc(first_name, email, token_url):
#     template_id = "d-d1a5edc8d0084ae8ab37c465cd854f7f"
#     return sendTemplateEmailDIFC(to=email,
#                                 template_id=template_id,
#                                 substition_data={
#                                     "token_url": token_url,
#                                     "First_Name": first_name
#                                 })


# def sendResetPasswordTiac(email, token_url):
#     template_id = "d-2db32ff2fde64b61bc5e49eadbbc400e"
#     return sendMultipleAttchmentsEmailTiac(to=email,
#                                         files=[],
#                                         tiac=True,
#                                         bcc_emails=[TIAC_PRODUCTION_CC_WEBNYAY],
#                                         template_id=template_id,
#                                         substition_data={
#                                             "token_url": token_url,
#                                         })


def sendOtpEmail(otp, email="", name="", reset_password=False):

    if reset_password:
        template_id = "d-51a8bf937a484cd5a6a5d8f81457de47"
    else:
        template_id = "d-d79856fdc8d04fe2bf94ff7de3bb0171"
    return sendTemplateEmail(to=email,
                            template_id=template_id,
                            substition_data={
                                "name": name,
                                "otp": otp
                            })


# def sendHotstarOtpEmail(otp, email=""):
#     template_id = "d-1654f0465e124fc8bcd110196d635192"
#     return sendTemplateEmail(to=email,
#                             template_id=template_id,
#                             substition_data={
#                                 "otp": otp
#                             })

# def sendMuthootOtpEmail(otp, email=""):
#     template_id = "d-bea5d099476e440fae795f4e519588fe"
#     return sendTemplateEmail(to=email,
#                             template_id=template_id,
#                             substition_data={
#                                 "otp": otp
#                             })

# def sendOrixOtpEmail(otp, email=""):
#     template_id = "d-b5498b816e87416d8f01e7efe95790af"
#     cc_emails = [WEBNYAY_EMAIL]
#     return sendTemplateEmail(to=email,
#                             template_id=template_id,
#                             substition_data={
#                                 "otp": otp
#                             }, cc_emails=cc_emails)


# def sendRVOtpEmail(otp, email="", name=""):
#     template_id = "d-125e94264a6846758ea3bd7adc797151"
#     return sendTemplateEmail(to=email,
#                             template_id=template_id,
#                             substition_data={
#                                 "name": name,
#                                 "otp": otp
#                             })


# def sendMeetingNotification(email="", name="", Dispute_Name="", disputeId="", Date_Time="", host="", profile=None, dispute_id_for_custom_args=""):
#     template_id = "d-231efa33e32a446b83d9e000332479f3"
#     template_id_cdr = "d-f567370d3ee7468f887d68d8dc11344e"
#     template_id_rv = "d-6342b6ca6140404c99fb8895a489ee60"
#     template_id_difc = "d-10d73478e7424de6b6c95c08fd6c6809"
#     template_id_vikalp = "d-bd95607e753f40b49fa24f9ad01268de"
#     template_id_tiac = "d-84fad82d793a4219868d0efaf86c8b8b"
#     custom_args = {"dispute_id": dispute_id_for_custom_args}
#     if profile and profile.group == 'difc':
#         return sendTemplateEmailDIFC(to=email,
#                                     template_id=template_id_difc,
#                                     substition_data={
#                                         "First_Name": name,
#                                         "dispute_name": Dispute_Name,
#                                         "date_time": Date_Time,
#                                     },
#                                     custom_args=custom_args)
#     elif host == 'cdr':
#         return sendTemplateEmail(to=email,
#                                 template_id=template_id_cdr,
#                                 substition_data={
#                                     "first_name": name,
#                                     "dispute_name": Dispute_Name,
#                                     "date_time": Date_Time,
#                                     "grievance_id": disputeId,
#                                     "calender_link": getPlatformLinkByHost(host)
#                                 },
#                                 custom_args=custom_args)
#     elif host == 'vikalp':
#         return sendTemplateEmail(to=email,
#                                 template_id=template_id_vikalp,
#                                 substition_data={
#                                     "first_name": name,
#                                     "dispute_name": Dispute_Name,
#                                     "date_time": Date_Time,
#                                     "dispute_id": disputeId,
#                                     "calender_link": getPlatformLinkByHost(host)
#                                 },
#                                 custom_args=custom_args)

#     elif host == 'tiac':
#         return sendMultipleAttchmentsEmailTiac(to=email,
#                                                 tiac=True,
#                                                 files=[],
#                                                 bcc_emails=[
#                                                     TIAC_PRODUCTION_CC,TIAC_PRODUCTION_BCC_WEBNYAY, ISHITA_MAIL_ID], template_id=template_id_tiac,
#                                                 substition_data={
#                                                     "case_name": Dispute_Name,
#                                                     "date": Date_Time,
#                                                     "case_id": disputeId,
#                                                 },
#                                                 custom_args=custom_args)
#     elif host == 'rv':
#         return sendTemplateEmail(to=email,
#                                 template_id=template_id_rv,
#                                 substition_data={
#                                     "first_name": name,
#                                     "dispute_name": Dispute_Name,
#                                     "date_time": Date_Time,
#                                     "grievance_id": disputeId,
#                                     "calender_link": getPlatformLinkByHost(host)
#                                 },
#                                 custom_args=custom_args)
#     else:
#         return sendTemplateEmail(to=email,
#                                 template_id=template_id,
#                                 substition_data={
#                                     "first_name": name,
#                                     "dispute_name": Dispute_Name,
#                                     "date_time": Date_Time,
#                                     "grievance_id": disputeId,
#                                     "calender_link": getPlatformLinkByHost(host)
#                                 },
#                                 custom_args=custom_args)


# def sendMeetingCancelEmail(email="", name="", Dispute_Name="", disputeId="", Date_Time="", dispute_id_for_custom_args={}):
#     template_id = "d-ca48ec21465c4c06a21af1c2e2421d95"
#     custom_args = {"dispute_id": dispute_id_for_custom_args}
#     return sendTemplateEmail(to=email,
#                             template_id=template_id,
#                             substition_data={
#                                 "name": name,
#                                 "title_name": Dispute_Name,
#                                 "date_time_zone": Date_Time,
#                                 "dispute_id": disputeId
#                             },
#                             custom_args=custom_args)


# def sendTestEmail(email="", subject="", value="test"):

#     send_email(to_email=email, subject=subject, value=value)


# def send_meeting_link_anonymous(email, meeting_link, first_name, transcript_link):
#     template_id = "d-a39566013f494841980f83f141db1556"

#     return send_email_template(
#         to_email=email,
#         template_id=template_id,
#         substition_data={
#             "first_name": first_name,
#             "meeting_link" : meeting_link,
#             "transcript_link": transcript_link
#         },
#         from_email="<EMAIL>",
#     )


def sendDIFCBulkDisputeCreatedRespondant(dispute, email, company_name, case_manager_name, dispute_id_for_custom_args=""):
    template_id = "d-7772905da2934e60b4fa35220e1a44c0"
    custom_args = {"dispute_id": dispute_id_for_custom_args}

    return send_email_template(
        to_email=email,
        template_id=template_id,
        substition_data={
            "company_name": company_name,
            "case_manager_name": case_manager_name
            },
        custom_args=custom_args
    )


# def sendProfileCreatedDIFC(email, first_name, password="", profile=""):
#     template_id = "d-68409b1c67924636be4c7b51411d24da"
#     return sendAttchmentEmailTiac(to=email,
#                                 template_id=template_id,
#                                 substition_data={
#                                     "First_Name": first_name,
#                                     'email': email,
#                                     'password': password,
#                                     'login_link': "https://moot.webnyay.in/login_difc",
#                                 },
#                                 profile=profile,
#                                 file=None,
#                                 file_name=None)


def sendProfileCreatedBFSI(email="", password="", link="", forget_password_link=""):
    if is_production_database():
        cced_emails = ["<EMAIL>"]
    else:
        cced_emails = ["<EMAIL>"]
    template_id = "d-8b9bef4f27ae4516aa9031f063923fd7"
    sendTemplateEmail(to=email,
                    template_id=template_id,
                    substition_data={
                    'user_name': email,
                    'password': password,
                    'portal_link': link,
                    'password_link': forget_password_link
                },cc_emails=cced_emails)


def send_email(to_email, template_id, substition_data={}, files=[], email_template=None):
    # If email_template is provided, check for is_s21_notice
    from_email = None
    if email_template.is_s21_notice:
        from_email = S21_MAIL_ID
    else:
        from_email = MAIL_ID

    return send_email_template(
        to_email=to_email,
        template_id=template_id,
        files=files,
        from_email=from_email,
        substition_data=substition_data,
        # cced_emails=['<EMAIL>']
    )


def send_arbitrator_assigned(email, arbitrator_name,dispute_name,dispute_number,case_manager_name,case_manager_mobile,case_manager_email):
    template_id = "d-98918623ab67411497f4e366b443bf54"
    if is_production_database():
        cced_emails = [case_manager_email,"<EMAIL>"]
    else:
        cced_emails = ["<EMAIL>"]


    return sendTemplateEmail(to=email,
                            template_id=template_id,
                            substition_data={
                                "arbitrator_name": arbitrator_name,
                                "case_name":dispute_name,
                                "case_number":dispute_number,
                                "case_manager_name":case_manager_name,
                                "case_manager_mobile":case_manager_mobile,
                                "case_manager_email":case_manager_email,

                            },
                            cc_emails=cced_emails,)


def sendBorrowerNotifyArbitratorAssigned(email="", borrower_name="", case_name="", case_id="",case_manager_email=""):
    if is_production_database():
        cced_emails = [case_manager_email,"<EMAIL>"]
    else:
        cced_emails = ["<EMAIL>"]
    template_id = "d-3c9ad41ed3314f0a9810b54c65825e64"
    sendTemplateEmail(to=email,
                    template_id=template_id,
                    substition_data={
                    'borrower_name': borrower_name,
                    'case_name': case_name,
                    'case_id': case_id,
                },cc_emails=cced_emails)


def send_delayed_email_arbirtrator_assigned(recipient_email, arbitrator_name):
    template_id = "d-52bcf432764e49c1b7641e013c3fe033"
    video_link = ""
    if is_production_database():
        cced_emails = ["<EMAIL>"]
    else:
        cced_emails = ["<EMAIL>"]
    sendTemplateEmail(to=recipient_email,
                    template_id=template_id,
                    substition_data={
                    'arbitrator_name': arbitrator_name,
                    'video_link':video_link
                },cc_emails=cced_emails)


def send_pfl_termination_notice(email, loan_number, borrowers_name, client_name, notice_date, notice_amount, payment_link, files=None):
    if files is None:
        files = []
    template_id = "d-4c707287dcc9481e851bfb29262d4728"

    return send_email_template(
        to_email=email,
        template_id=template_id,
        substition_data={
            "borrower_name": borrowers_name,
            "client_name": client_name,
            "notice_date": notice_date,
            "notice_amount": notice_amount,
            "payment_link": payment_link,
            "loan_number": loan_number
        },
        from_email=S21_MAIL_ID,
        files=files
    )
