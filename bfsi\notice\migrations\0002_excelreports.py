# Generated by Django 3.2 on 2025-04-01 10:40

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('notice', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ExcelReports',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('folder_name', models.Char<PERSON>ield(max_length=255)),
                ('file_name', models.Char<PERSON>ield(max_length=255)),
                ('file_path', models.CharField(max_length=255)),
                ('message_id', models.CharField(blank=True, max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='notice.user')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
