# Generated by Django 3.2 on 2025-04-10 05:37

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('notice', '0007_auto_20250408_1236'),
    ]

    operations = [
        migrations.CreateModel(
            name='WhatsAppTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('template_id', models.CharField(max_length=100, unique=True)),
                ('name', models.CharField(max_length=255)),
                ('body', models.TextField(help_text='Full body of the template')),
                ('lang_code', models.CharField(max_length=10)),
                ('requires_attachment', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTime<PERSON>ield(auto_now=True)),
            ],
        ),
        migrations.Add<PERSON>ield(
            model_name='campaign',
            name='whatsapp_status',
            field=models.Char<PERSON>ield(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='campaign',
            name='whatsapp_template',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='notice.whatsapptemplate'),
        ),
    ]
