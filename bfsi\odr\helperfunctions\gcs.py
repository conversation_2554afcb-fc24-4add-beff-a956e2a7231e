import datetime
from typing import Any, Union

from google.api_core.exceptions import NotFound
from google.cloud import storage
from google.oauth2 import service_account
from pytz import timezone

from bfsi.settings import GCS_BUCKET_NAME, STORAGE_CREDENTIALS


class GoogleCloudStorageManager:
    """
    Provides comprehensive utilities for interacting with Google Cloud Storage.
    """

    def __init__(self, credentials: service_account.Credentials, bucket_name: str):
        """
        Initialize the storage client with specific credentials and bucket.

        Args:
            credentials: GCP service account credentials
            bucket_name: Name of the GCS bucket
        """
        self._storage_client = storage.Client(credentials=credentials)
        self._bucket_name = bucket_name

    def upload_file_with_content_type_and_disiposition(self, path: str, data: Union[bytes, Any], content_type: str = 'application/pdf', content_disposition: str = 'attachment'):
        """
        Upload a file to the specified bucket.

        Args:
            path: Destination path in the bucket
            data: File-like object or byte content to upload
        """
        try:
            bucket = self._storage_client.bucket(self._bucket_name)
            blob = bucket.blob(path)
            blob.content_type = content_type
            blob.content_disposition = content_disposition
            blob.upload_from_file(data) if hasattr(
                data, 'read') else blob.upload_from_string(data)
        except Exception as e:
            raise IOError(f"Failed to upload file to {path}: {str(e)}")


    def upload_file(self, path: str, data: Union[bytes, Any]):
        """
        Upload a file to the specified bucket.

        Args:
            path: Destination path in the bucket
            data: File-like object or byte content to upload
        """
        try:
            bucket = self._storage_client.bucket(self._bucket_name)
            blob = bucket.blob(path)
            if hasattr(data, 'read'):
                # Reset file pointer to beginning before upload
                if hasattr(data, 'seek'):
                    data.seek(0)
                blob.upload_from_file(data)
            else:
                blob.upload_from_string(data)
        except Exception as e:
            raise IOError(f"Failed to upload file to {path}: {str(e)}")

    def delete_file(self, path: str):
        """
        Delete a file from the bucket.

        Args:
            path: Path of the file to delete
        """
        try:
            bucket = self._storage_client.bucket(self._bucket_name)
            blob = bucket.blob(path)
            blob.delete()
        except NotFound:
            print(f"File {path} not found in bucket.")
        except Exception as e:
            raise IOError(f"Failed to delete file {path}: {str(e)}")

    def read_file(self, path: str) -> bytes:
        """
        Read a file from the bucket.

        Args:
            path: Path of the file to read

        Returns:
            File content as bytes
        """
        try:
            bucket = self._storage_client.bucket(self._bucket_name)
            blob = bucket.blob(path)
            return blob.download_as_string()
        except Exception as e:
            raise IOError(f"Failed to read file {path}: {str(e)}")

    def rename_file(self, old_path: str, new_path: str):
        """
        Rename a file in the bucket.

        Args:
            old_path: Current file path
            new_path: Destination file path
        """
        try:
            bucket = self._storage_client.bucket(self._bucket_name)
            old_blob = bucket.blob(old_path)
            new_blob = bucket.blob(new_path)
            new_blob.rewrite(old_blob)
            old_blob.delete()
        except Exception as e:
            raise IOError(
                f"Failed to rename file from {old_path} to {new_path}: {str(e)}")

    def download_file(self, object_path: str, local_path: str):
        """
        Download a file from the bucket to local storage.

        Args:
            object_path: Path of the file in the bucket
            local_path: Local destination path
        """
        try:
            bucket = self._storage_client.bucket(self._bucket_name)
            blob = bucket.blob(object_path)
            blob.download_to_filename(local_path)
        except Exception as e:
            raise IOError(f"Failed to download file {object_path}: {str(e)}")

    def generate_signed_url(self, path: str, expiration_seconds: int = 3600) -> str:
        """
        Generate a signed URL for a file.

        Args:
            path: Path of the file
            expiration_seconds: URL validity duration

        Returns:
            Signed URL string
        """
        try:
            bucket = self._storage_client.bucket(self._bucket_name)
            blob = bucket.blob(path)

            # Use timezone-aware datetime with UTC
            now = datetime.datetime.now(timezone('UTC'))
            expiration_time = now + \
                datetime.timedelta(seconds=expiration_seconds)

            return blob.generate_signed_url(expiration=int(expiration_time.timestamp()))
        except Exception as e:
            raise ValueError(
                f"Failed to generate signed URL for {path}: {str(e)}")
    
    def list_files(self, prefix: str = None) -> list:
        """
        List files in the bucket.

        Args:
            prefix: Prefix to filter files

        Returns:
            List of file paths
        """
        try:
            bucket = self._storage_client.bucket(self._bucket_name)
            return [blob.name for blob in bucket.list_blobs(prefix=prefix)]
        except Exception as e:
            raise IOError(f"Failed to list files in bucket: {str(e)}")

    def list_files_recursively(self, prefix: str = None, file_extension: str = None) -> list:
        """
        List files recursively in the bucket, including nested folders.

        Args:
            prefix: Prefix to filter files
            file_extension: Filter by file extension (e.g., '.pdf')

        Returns:
            List of file paths
        """
        try:
            bucket = self._storage_client.bucket(self._bucket_name)
            blobs = bucket.list_blobs(prefix=prefix)
            
            file_list = []
            for blob in blobs:
                # Skip folders (blobs ending with '/')
                if not blob.name.endswith('/'):
                    if file_extension is None or blob.name.lower().endswith(file_extension.lower()):
                        file_list.append(blob.name)
            
            return file_list
        except Exception as e:
            raise IOError(f"Failed to list files recursively in bucket: {str(e)}")

    def file_exists(self, path: str) -> bool:
        """
        Check if a file exists in the bucket.

        Args:
            path: Path of the file to check

        Returns:
            True if the file exists, False otherwise
        """
        try:
            bucket = self._storage_client.bucket(self._bucket_name)
            blob = bucket.blob(path)
            return blob.exists()
        except Exception as e:
            raise IOError(f"Failed to check file existence for {path}: {str(e)}")

    def generate_nested_filename(self, full_path: str, base_folder_path: str) -> str:
        """
        Generate a filename for nested files by replacing path separators with underscores.
        
        Args:
            full_path: Full path of the file in GCS
            base_folder_path: Base folder path to remove from the beginning
            
        Returns:
            Formatted filename with underscores
        """
        # Remove the base folder path from the beginning
        if full_path.startswith(base_folder_path):
            relative_path = full_path[len(base_folder_path):]
        else:
            relative_path = full_path
        
        # Remove leading slash if present
        if relative_path.startswith('/'):
            relative_path = relative_path[1:]
        
        # Replace path separators with underscores
        # Keep the original file extension
        filename_with_path = relative_path.replace('/', '_')
        
        return filename_with_path


gcs_manager = GoogleCloudStorageManager(
    credentials=STORAGE_CREDENTIALS,
    bucket_name=GCS_BUCKET_NAME
)
