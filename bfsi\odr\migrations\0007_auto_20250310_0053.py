# Generated by Django 3.2 on 2025-03-09 19:23

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('odr', '0006_profile_company'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='dispute',
            name='hotstar_email_11_days_remainder',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='hotstar_email_14_days_remainder',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='hotstar_email_15_days_remainder',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='hotstar_email_16_hour_remainder',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='hotstar_email_7_days_remainder',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='muthoot_status',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='sebi_email_2_days_remainder',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='seven_days_reminder_tiac',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='three_days_reminder_tiac',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='tiac_custom_dispute_id',
        ),
    ]
