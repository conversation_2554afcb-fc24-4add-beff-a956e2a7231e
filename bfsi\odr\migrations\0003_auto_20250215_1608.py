# Generated by Django 3.2 on 2025-02-15 10:38

from django.db import migrations, models
import django.db.models.deletion
import django_extensions.db.fields


class Migration(migrations.Migration):

    dependencies = [
        ('odr', '0002_auto_20250215_1455'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='dispute',
            name='company_product',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='tiac_dispute_step',
        ),
        migrations.CreateModel(
            name='CaseFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name='created')),
                ('modified', django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name='modified')),
                ('summary', models.Char<PERSON>ield(max_length=5000)),
                ('description', models.Char<PERSON>ield(max_length=10000)),
                ('case_status', models.CharField(choices=[('new', 'new'), ('in_progress', 'in_progress'), ('dispute_lodged', 'dispute_lodged'), ('questions_complete', 'questions_complete'), ('upload_complete', 'upload_complete'), ('review_complete', 'review_complete'), ('complete', 'complete')], default='new', max_length=300)),
                ('type', models.CharField(choices=[('claimant', 'claimant'), ('respondent', 'respondent'), ('io', 'io'), ('md', 'md'), ('nodal', 'nodal'), ('gro', 'gro')], max_length=300)),
                ('proposed_flow', models.CharField(choices=[('negotiation', 'negotiation'), ('mediation', 'mediation'), ('arbitration', 'arbitration'), ('adjudication', 'adjudication'), ('grievance', 'grievance')], default='arbitration', max_length=300)),
                ('review_complete_date', models.DateTimeField(blank=True, null=True)),
                ('dispute', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='odr.dispute')),
                ('doc', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='odr.legaldocument')),
                ('expert', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='expert', to='odr.profile')),
                ('lawyer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='lawyer', to='odr.profile')),
                ('lawyer_rv', models.ManyToManyField(blank=True, default=None, related_name='lawyer_rv', to='odr.Profile')),
                ('profile', models.ForeignKey(null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='profile', to='odr.profile')),
                ('witness', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='witness', to='odr.profile')),
            ],
            options={
                'get_latest_by': 'modified',
                'abstract': False,
            },
        ),
    ]
