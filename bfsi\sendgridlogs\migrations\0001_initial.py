# Generated by Django 3.2 on 2024-01-10 06:00

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SendgridMail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email', models.CharField(max_length=120)),
                ('x_message_id', models.CharField(max_length=120, unique=True)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='SendgridEventEntry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event', models.CharField(choices=[('processed', 'Processed'), ('dropped', 'Dropped'), ('delivered', 'Delivered'), ('deferred', 'Deferred'), ('bounce', 'Bounce'), ('open', 'Open')], max_length=10)),
                ('timestamp', models.DateTimeField(blank=True, null=True)),
                ('sg_event_id', models.CharField(blank=True, default=None, max_length=120, null=True, unique=True)),
                ('smtp_id', models.CharField(max_length=120)),
                ('reason', models.CharField(blank=True, max_length=100, null=True)),
                ('sendgrid_mail', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='sendgridlogs.sendgridmail')),
            ],
        ),
    ]
