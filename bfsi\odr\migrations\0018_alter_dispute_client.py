# Generated by Django 3.2 on 2025-04-30 08:22

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('odr', '0017_alter_dispute_arbitrator_rv'),
    ]

    operations = [
        migrations.AlterField(
            model_name='dispute',
            name='client',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
    ]
