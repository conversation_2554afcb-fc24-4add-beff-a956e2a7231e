from django.contrib import admin

from sendgridlogs.models import SendgridEventEntry, SendgridMail


@admin.register(SendgridMail)
class SendgridMailAdmin(admin.ModelAdmin):
    list_display = ('email', 'x_message_id', 'dispute_id', 'subject')  
    search_fields = ('email', 'x_message_id', 'dispute_id', 'subject')  

@admin.register(SendgridEventEntry)
class SendgridEventEntryAdmin(admin.ModelAdmin):
    list_display = ('event', 'sendgrid_mail', 'timestamp',)
    search_fields = ('sendgrid_mail__email', 'sg_event_id', 'smtp_id')
    list_filter = ('event',)
