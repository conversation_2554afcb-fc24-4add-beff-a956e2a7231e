from io import BytesIO
import json
import requests
from bfsi.settings import WHATSAPP_CONFIG_BFSI, db_logger, IS_S21_WHATSAPP_CONFIG

def send_message(data, is_s21_notice=None):
    if is_s21_notice:
        WHATSAPP_CONFIG = IS_S21_WHATSAPP_CONFIG
    else:
        WHATSAPP_CONFIG = WHATSAPP_CONFIG_BFSI

    headers = {
        "Content-type": "application/json",
        "Authorization": f"Bearer {WHATSAPP_CONFIG['ACCESS_TOKEN']}",
    }

    url = f"https://graph.facebook.com/{WHATSAPP_CONFIG['VERSION']}/{WHATSAPP_CONFIG['PHONE_NUMBER_ID']}/messages"
    try:
        response = requests.post(url, headers=headers, data=data)
        if response.status_code == 200:
            print("Status:", response.status_code)
            print("Content-type:", response.headers['content-type'])
            html = response.text
            print("Body:", html, type(html))
            return html
        else:
            print(response.status_code)
            print(response.text)
            db_logger.exception(
                "Error processing the whatsapp message request " + response.text)
    except requests.exceptions.RequestException as e:
        print('Connection Error:', str(e))


def send_whatsapp_message_test(mobile, template_id, is_s21_notice, body_param_text=None, media_id=None, file_name=None, lang_code="en", has_body_params=None, parameters=None):
    """Enhanced test function with conciliation support"""
    template = {
        "name": template_id,
        "language": {"code": lang_code}
    }

    components = []

    # Add media/document header only if provided
    if media_id:
        components.append({
            "type": "header",
            "parameters": [
                {
                    "type": "document",
                    "document": {
                        "id": media_id,
                        "filename": file_name or "Document"
                    }
                }
            ]
        })

    # Handle body parameters - NEW: Support for multiple parameters
    if parameters and isinstance(parameters, list) and len(parameters) > 0:
        # Multiple parameters for conciliation templates
        components.append({
            "type": "body",
            "parameters": [
                {
                    "type": "text",
                    "text": str(param)
                } for param in parameters
            ]
        })
    elif body_param_text and has_body_params is True:
        # Single body parameter for regular templates
        components.append({
            "type": "body",
            "parameters": [
                {
                    "type": "text",
                    "text": body_param_text
                }
            ]
        })

    # Only add components if there are any
    if components:
        template["components"] = components

    data = {
        "messaging_product": "whatsapp",
        "to": mobile,
        "type": "template",
        "template": template
    }

    return send_message(json.dumps(data), is_s21_notice)


def upload_media_to_whatsapp(file_data, file_name, is_s21_notice=None):
    """
    Uploads a file to WhatsApp Cloud API and returns the media ID.
    Ensures file name is shown in WhatsApp document preview.
    """
    if is_s21_notice:
        WHATSAPP_CONFIG = IS_S21_WHATSAPP_CONFIG
    else:
        WHATSAPP_CONFIG = WHATSAPP_CONFIG_BFSI

    url = f"https://graph.facebook.com/{WHATSAPP_CONFIG['VERSION']}/{WHATSAPP_CONFIG['PHONE_NUMBER_ID']}/media"

    headers = {
        "Authorization": f"Bearer {WHATSAPP_CONFIG['ACCESS_TOKEN']}"
    }

    file_stream = BytesIO(file_data)
    files = {
        'file': (file_name, file_stream, 'application/pdf')
    }

    data = {
        'messaging_product': 'whatsapp',
        'type': 'document'
    }

    params = {
        'filename': file_name  
    }

    try:
        response = requests.post(url, headers=headers, files=files, data=data, params=params, timeout=10)
        if response.status_code == 200:
            print("Status:", response.status_code)
            print("Response:", response.text)
            return json.loads(response.text)
        else:
            print("Error:", response.status_code, response.text)
            db_logger.exception("Error uploading media to WhatsApp: %s", response.text)
    except requests.exceptions.RequestException as e:
        print("Connection Error:", str(e))
        db_logger.exception("Connection Error uploading media to WhatsApp: %s", str(e))


def send_whatsapp_pfl(mobile, media_id, file_name, borrowers_name, client_name, notice_date, notice_amount, payment_link, loan_acc_number):
    template_id = 'payment_request_demand_notice_issued_against'
    lang_code = 'en_US'
    data = json.dumps({
        "messaging_product": "whatsapp",
        "to": mobile,
        "type": "template",
        "template": {
            "name": template_id,
            "language": {
                "code": lang_code
            },
            "components": [
                {
                    "type": "header",
                    "parameters": [
                        {
                            "type": "document",
                            "document": {
                                "id": media_id,
                                "filename" : file_name
                            }
                        }
                    ]
                },
                {
                    "type": "body",
                    "parameters": [
                        {
                            "type": "text",
                            "text": borrowers_name
                        },
                        {
                            "type": "text",
                            "text": client_name
                        },
                        {
                            "type": "text",
                            "text": loan_acc_number
                        },
                        {
                            "type": "text",
                            "text": notice_date
                        },
                        {
                            "type": "text",
                            "text": notice_amount
                        },
                        {
                            "type": "text",
                            "text": notice_amount
                        },
                        {
                            "type": "text",
                            "text": payment_link
                        }
                    ]
                }
            ]
        }
    })
    return send_message(data, is_s21_notice=True)