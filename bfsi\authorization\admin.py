from __future__ import unicode_literals
from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from authorization.models import EmailOtp, User


@admin.register(User)
class CustomUserAdmin(UserAdmin):
    list_display = ('email', 'first_name', 'last_name', 'date_joined', 'is_active',
                    'temp_password_email_sent')
    list_filter = ('date_joined', 'is_active',
                   'temp_password_email_sent')
    search_fields = ('id', 'email', 'first_name',
                     'last_name')
    ordering = ('-date_joined',)
    readonly_fields = ('last_login', 'date_joined')

    fieldsets = (
        (None, {'fields': ('username', 'password')}),
        ('Personal info', {'fields': ('first_name', 'last_name',
         'email', 'temp_password_email_sent')}),
        ('Permissions', {'fields': ('is_active', 'is_staff',
         'is_superuser', 'groups', 'user_permissions')}),
        ('Important dates', {'fields': ('last_login', 'date_joined')}),
    )


@admin.register(EmailOtp)
class EmailOtpAdmin(admin.ModelAdmin):
    list_display = ('email', 'otp', 'name', 'date_created')
    list_filter = ('is_active',)
    search_fields = ('email', 'name')
