# Generated by Django 3.2 on 2025-05-20 07:13

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('odr', '0023_alter_dispute_co_borrowers'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='dispute',
            name='approved',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='approved_date',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='city',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='converted_to_arbitration_at',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='dateclosed_level1',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='dateclosed_level2',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='level_1_compete',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='level_2_closed',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='level_of_dispute',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='rv_arbitrator_step_complete',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='rv_claimant_choose_step_complete',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='rv_claimant_lawyer_step_complete',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='rv_respondent_choose_step_complete',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='rv_respondent_lawyer_step_complete',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='state',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='summery',
        ),
    ]
