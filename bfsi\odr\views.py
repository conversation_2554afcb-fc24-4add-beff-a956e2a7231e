import os
import time
import json
import csv
import zipfile
from django.shortcuts import get_object_or_404
from io import BytesIO
import pandas as pd
import requests
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser
from rest_framework import serializers
from django.utils import timezone
from django.db import connections, transaction
from authorization.models import User
from authorization.serializers import UserSerializer
from sendgridlogs.models import SendgridEventEntry, SendgridMail
from odr.permissions import IsAdmin, IsAnyRole, IsClientAndSelf, IsAdminOrClient, IsCaseManager, IsArbitrator
from bfsi.settings import CREDENTIALS_DICT, GCP_PROJECT_ID, GCS_BUCKET_NAME, PUBSUB_BULK_CASE_CREATION
from notice.models import Campaign, CampaignTemplate, Notice, Template
from odr.utils import extract_common_data, validate_and_normalize_excel_file, get_timestamped_filename
from odr.mailhelpers import sendDIFCBulkDisputeCreatedRespondant,send_arbitrator_assigned,sendBorrowerNotifyArbitratorAssigned,send_delayed_email_arbirtrator_assigned
from odr.helperfunctions.databasefunctions import is_production_database
from odr.serializers import CustomUserSerializer, CampaignSerializer, CaseFileListSerializer, ProfileInfoSerializer, ProfileMajorDetailSerializer
from odr.models import Dispute, Profile, ProfileType, File, Folder
from rest_framework.generics import CreateAPIView
# from rest_framework_jwt.authentication import JSONWebTokenAuthentication
from rest_framework.permissions import AllowAny , IsAuthenticated
from django.http import HttpResponse, StreamingHttpResponse
from odr.helperfunctions.gcs import gcs_manager
from django.contrib.auth.hashers import make_password
from django.http import JsonResponse
from google.cloud import pubsub_v1
from google.oauth2 import service_account
from django.db.models import Q
from datetime import datetime,timedelta
from notice.models import Entry
from rest_framework.pagination import PageNumberPagination
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from notice.serializers import NoticeSerializer
from django.utils import timezone
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from google.oauth2 import service_account
import threading
from time import sleep
from django.db.models import Prefetch
import mimetypes
from concurrent.futures import ThreadPoolExecutor, as_completed
import traceback
from notice.validation_utils import validate_profile_campaign,validate_profile_dispute, validate_profile_notice, validate_profile_loan_id,validate_profile_client,validate_profile_case_manager,validate_profile_arbitrator


class ProfileGet(APIView):
    def get(self, request):
        user = self.request.user
        profile = get_object_or_404(Profile, user=user)

        # Serialize user data
        user_serializer = UserSerializer(profile.user)

        # Construct response dictionary manually
        profile_data = {
            "id": profile.id,
            "auth_type": profile.auth_type,
            "profile_type": profile.profile_type,
            "created_by_process": profile.created_by_process,
            "created_by": profile.created_by.id if profile.created_by else None,
            "phone_number": profile.phone_number,
            "date_created": profile.created,
            "date_modified": profile.modified,
            "user": user_serializer.data
        }

        return JsonResponse(data=profile_data, status=200)


class CustomUserSignupView(CreateAPIView):
    serializer_class = CustomUserSerializer


class UserManagementView(APIView):
    permission_classes = [IsAuthenticated, IsAdminOrClient]

    def get(self, request, id=None):
        """
        Get user(s). If id is provided, returns the specific user.
        Otherwise, returns a list of users.
        """
        profile_type = request.query_params.get('profile_type')
        search_text = request.query_params.get('search_text')
        client = request.query_params.get('client')
        show_pagination = request.query_params.get('show_pagination', 'false').lower() == 'true'
        page = request.query_params.get('page', 1)
        page_size = request.query_params.get('page_size', 10)
        stats = None

        user = request.user        
        
        is_admin=False
        is_client=False
        is_sub_client = False
        is_arbitrator = False
        is_case_manager = False

        # Check user's profile type
        current_profile = Profile.objects.get(user=user)
        current_profile_type = current_profile.profile_type

        if current_profile_type == ProfileType.admin.name:
            is_admin=True
        elif current_profile_type == ProfileType.client.name:
            is_client=True
        elif current_profile_type == ProfileType.sub_client.name:
            is_sub_client=True
        elif current_profile_type == ProfileType.case_manager.name:
            is_case_manager=True
        elif current_profile_type == ProfileType.arbitrator.name:
            is_arbitrator = True

        if id:
            # Retrieve a single user by ID
            profile = get_object_or_404(Profile, user__id=id)

            if current_profile.id == profile.id:
                pass
            
            # Apply RBAC for viewing individual users
            elif is_admin:
                # Admin can view any user
                pass
            elif is_client or is_sub_client:
                # Client can only view their own profile, their sub_clients, or users related to their disputes
                client_profile = Profile.objects.get(user=user, profile_type__in=[ProfileType.client.name, ProfileType.sub_client.name])
                
                # Allow viewing sub_clients if parent client matches
                if profile.profile_type == ProfileType.sub_client.name and hasattr(profile, 'parent_client') and profile.parent_client == client_profile:
                    pass
                # For case managers and arbitrators, check if they're assigned to client's disputes
                elif profile.profile_type in [ProfileType.case_manager.name, ProfileType.arbitrator.name]:
                    client_disputes = Dispute.objects.filter(client=client_profile)
                    
                    if profile.profile_type == ProfileType.case_manager.name:
                        has_access = client_disputes.filter(case_manager_rv=profile).exists()
                    else:  # arbitrator
                        has_access = client_disputes.filter(arbitrator_rv=profile).exists()
                    
                    if not has_access:
                        return Response({"error": "Not authorized to view this user's data"}, status=status.HTTP_403_FORBIDDEN)
                else:
                    return Response({"error": "Not authorized to view this user's data"}, status=status.HTTP_403_FORBIDDEN)
            elif is_case_manager:
                # Case manager can view their own profile and arbitrators assigned to their disputes                
                
                # Allow viewing arbitrators assigned to case manager's disputes
                if profile.profile_type == ProfileType.arbitrator.name:
                    case_manager_disputes = Dispute.objects.filter(case_manager_rv=current_profile)
                    has_access = case_manager_disputes.filter(arbitrator_rv=profile).exists()
                    
                    if not has_access:
                        return Response({"error": "Not authorized to view this user's data"}, status=status.HTTP_403_FORBIDDEN)
                else:
                    return Response({"error": "Not authorized to view this user's data"}, status=status.HTTP_403_FORBIDDEN)
            elif is_arbitrator:
                # Arbitrator can only view their own profile and case managers who assigned them cases
                arbitrator_profile = Profile.objects.get(user=user, profile_type=ProfileType.arbitrator.name)
                
                # Allow viewing case managers who assigned disputes to this arbitrator
                if profile.profile_type == ProfileType.case_manager.name:
                    arbitrator_disputes = Dispute.objects.filter(arbitrator_rv=current_profile)
                    has_access = arbitrator_disputes.filter(case_manager_rv=profile).exists()
                    
                    if not has_access:
                        return Response({"error": "Not authorized to view this user's data"}, status=status.HTTP_403_FORBIDDEN)
                else:
                    return Response({"error": "Not authorized to view this user's data"}, status=status.HTTP_403_FORBIDDEN)

            # Calculate stats for the profile
            if profile.profile_type == "case_manager" or profile.profile_type == "arbitrator":
                if profile.profile_type == "case_manager":
                    disputes = Dispute.objects.filter(case_manager_rv__id=profile.id)
                    # Filter disputes by client if the current user is a client
                    if is_client:
                        disputes = disputes.filter(client=current_profile)
                elif profile.profile_type == "arbitrator":
                    disputes = Dispute.objects.filter(arbitrator_rv__id=profile.id)
                    # Filter disputes by client if the current user is a client
                    if is_client:
                        disputes = disputes.filter(client=current_profile)

                conciliation_active = disputes.filter(flow_type='conciliation').exclude(status='closed').count()
                arbitration_active = disputes.filter(flow_type='arbitration').exclude(status='closed').count()
                closed_cases = disputes.filter(status='closed').count()

                stats = {
                    "active_conciliation": conciliation_active,
                    "active_arbitration": arbitration_active,
                    "closed": closed_cases,
                    "total_cases_managed":int(conciliation_active)+int(arbitration_active)+int(closed_cases),
                    "ongoing_cases": int(conciliation_active)+int(arbitration_active)
                }
            elif profile.profile_type == "client":
                disputes = Dispute.objects.filter(client__id=profile.id)
                # If current user is a client, they should only see their own data
                if is_client and profile.id != current_profile.id:
                    return Response({"error": "Not authorized to view this client's data"}, status=status.HTTP_403_FORBIDDEN)

                conciliation_active = disputes.filter(flow_type='conciliation').exclude(status='closed').count()
                arbitration_active = disputes.filter(flow_type='arbitration').exclude(status='closed').count()
                closed_cases = disputes.filter(status='closed').count()
                now = timezone.now()
                current_month_cases = disputes.filter(
                    created__year=now.year,
                    created__month=now.month
                ).count()

                stats = {
                    "active_conciliation": conciliation_active,
                    "active_arbitration": arbitration_active,
                    "closed": closed_cases,
                    "total_cases":int(conciliation_active)+int(arbitration_active)+int(closed_cases),
                    "ongoing_cases": int(conciliation_active)+int(arbitration_active),
                    "monthly_cases": current_month_cases
                }

            serializer = UserSerializer(profile.user)
            response_data = serializer.data
            # Add phone number to the response
            response_data["phone_number"] = profile.phone_number
            if stats:
                response_data["stats"] = stats
            return Response(response_data)
        else:
            # List all users with RBAC filtering
            if profile_type and profile_type in ["claimant", "sub_client"] and not client:
                return Response({"error":"client is required"})
            elif profile_type and profile_type in ["claimant", "sub_client"] and client:
                profiles = Profile.objects.filter(profile_type=profile_type, parent_client__user=client).order_by('-id')
            else:
                # Apply RBAC for listing users
                if is_admin:
                    # Admin can see all profiles
                    profiles = Profile.objects.all().order_by('-id') if profile_type is None else Profile.objects.filter(profile_type=profile_type).order_by('-id')
                elif is_client:                   
                    
                    if profile_type == "client":
                        # Clients can only see themselves
                        profiles = Profile.objects.filter(id=current_profile.id).order_by('-id')
                    elif profile_type == "sub_client":
                        # Clients can see their sub-clients
                        profiles = Profile.objects.filter(profile_type=ProfileType.sub_client.name, parent_client=current_profile).order_by('-id')
                    elif profile_type == "case_manager":
                        # Clients can see case managers assigned to their disputes
                        client_disputes = Dispute.objects.filter(client=current_profile)
                        case_manager_ids = client_disputes.values_list('case_manager_rv__id', flat=True).distinct()
                        profiles = Profile.objects.filter(id__in=case_manager_ids).order_by('-id')
                    elif profile_type == "arbitrator":
                        # Clients can see arbitrators assigned to their disputes
                        client_disputes = Dispute.objects.filter(client=current_profile)
                        arbitrator_ids = client_disputes.values_list('arbitrator_rv__id', flat=True).distinct()
                        profiles = Profile.objects.filter(id__in=arbitrator_ids).order_by('-id')
                    else:
                        # Default to showing only profiles related to client's disputes
                        client_disputes = Dispute.objects.filter(client=current_profile)
                        case_manager_ids = client_disputes.values_list('case_manager_rv__id', flat=True).distinct()
                        arbitrator_ids = client_disputes.values_list('arbitrator_rv__id', flat=True).distinct()
                        profiles = Profile.objects.filter(
                            Q(id=current_profile.id) |  # Include self
                            Q(profile_type=ProfileType.sub_client.name, parent_client=current_profile) |  # Include sub-clients
                            Q(id__in=case_manager_ids) |  # Include case managers
                            Q(id__in=arbitrator_ids)  # Include arbitrators
                        ).order_by('-id')
                        if profile_type:
                            profiles = profiles.filter(profile_type=profile_type)
                elif is_sub_client:
                    client_profile = Profile.objects.get(id=current_profile.parent_client.id)
                    
                    if profile_type == "client":
                        # Clients can only see themselves
                        profiles = Profile.objects.filter(id=client_profile.id).order_by('-id')
                    elif profile_type == "sub_client":
                        # Clients can see their sub-clients
                        profiles = Profile.objects.filter(profile_type=ProfileType.sub_client.name, parent_client=client_profile).order_by('-id')
                    elif profile_type == "case_manager":
                        # Clients can see case managers assigned to their disputes
                        client_disputes = Dispute.objects.filter(client=client_profile)
                        case_manager_ids = client_disputes.values_list('case_manager_rv__id', flat=True).distinct()
                        profiles = Profile.objects.filter(id__in=case_manager_ids).order_by('-id')
                    elif profile_type == "arbitrator":
                        # Clients can see arbitrators assigned to their disputes
                        client_disputes = Dispute.objects.filter(client=client_profile)
                        arbitrator_ids = client_disputes.values_list('arbitrator_rv__id', flat=True).distinct()
                        profiles = Profile.objects.filter(id__in=arbitrator_ids).order_by('-id')
                    else:
                        # Default to showing only profiles related to client's disputes
                        client_disputes = Dispute.objects.filter(client=client_profile)
                        case_manager_ids = client_disputes.values_list('case_manager_rv__id', flat=True).distinct()
                        arbitrator_ids = client_disputes.values_list('arbitrator_rv__id', flat=True).distinct()
                        profiles = Profile.objects.filter(
                            Q(id=client_profile.id) |  # Include self
                            Q(profile_type=ProfileType.sub_client.name, parent_client=client_profile) |  # Include sub-clients
                            Q(id__in=case_manager_ids) |  # Include case managers
                            Q(id__in=arbitrator_ids)  # Include arbitrators
                        ).order_by('-id')
                        if profile_type:
                            profiles = profiles.filter(profile_type=profile_type)
                elif is_case_manager:                    
                    
                    if profile_type == "case_manager":
                        # Case managers can see all case managers
                        profiles = Profile.objects.filter(profile_type=ProfileType.case_manager.name).order_by('-id')
                        
                    elif profile_type == "arbitrator":
                        # Case managers can see all arbitrators
                        profiles = Profile.objects.filter(profile_type=ProfileType.arbitrator.name).order_by('-id')
                    elif profile_type == "client":
                        # Case managers can see clients whose disputes they manage
                        case_manager_disputes = Dispute.objects.filter(case_manager_rv=current_profile)
                        client_ids = case_manager_disputes.values_list('client__id', flat=True).distinct()
                        profiles = Profile.objects.filter(id__in=client_ids).order_by('-id')
                    else:
                        # Default to showing case manager's profile, all arbitrators, and related clients
                        case_manager_disputes = Dispute.objects.filter(case_manager_rv=current_profile)
                        client_ids = case_manager_disputes.values_list('client__id', flat=True).distinct()
                        profiles = Profile.objects.filter(
                            Q(id=current_profile.id) |  # Include self
                            Q(id__in=client_ids) |  # Include clients
                            Q(profile_type=ProfileType.arbitrator.name)  # Include all arbitrators
                        ).order_by('-id')
                        if profile_type:
                            profiles = profiles.filter(profile_type=profile_type)
                elif is_arbitrator:                    
                    
                    if profile_type == "arbitrator":
                        # Arbitrators can only see themselves
                        profiles = Profile.objects.filter(profile_type=ProfileType.arbitrator.name).order_by('-id')
                    elif profile_type == "case_manager":
                        # Arbitrators can see case managers who assigned them disputes
                        arbitrator_disputes = Dispute.objects.filter(arbitrator_rv=current_profile)
                        case_manager_ids = arbitrator_disputes.values_list('case_manager_rv__id', flat=True).distinct()
                        profiles = Profile.objects.filter(id__in=case_manager_ids).order_by('-id')
                    elif profile_type == "client":
                        # Arbitrators can see clients whose disputes they're assigned to
                        arbitrator_disputes = Dispute.objects.filter(arbitrator_rv=current_profile)
                        client_ids = arbitrator_disputes.values_list('client__id', flat=True).distinct()
                        profiles = Profile.objects.filter(id__in=client_ids).order_by('-id')
                    else:
                        # Default to showing only profiles related to arbitrator's disputes
                        arbitrator_disputes = Dispute.objects.filter(arbitrator_rv=current_profile)
                        client_ids = arbitrator_disputes.values_list('client__id', flat=True).distinct()
                        case_manager_ids = arbitrator_disputes.values_list('case_manager_rv__id', flat=True).distinct()
                        profiles = Profile.objects.filter(
                            Q(id=arbitrator_profile.id) |  # Include self
                            Q(id__in=client_ids) |  # Include clients
                            Q(id__in=case_manager_ids)  # Include case managers
                        ).order_by('-id')
                        if profile_type:
                            profiles = profiles.filter(profile_type=profile_type)
                else:
                    # Default to empty queryset for unknown profile types
                    profiles = Profile.objects.none()

            # Apply search filter if provided
            if search_text:
                profiles = profiles.filter(
                    Q(user__first_name__icontains=search_text) |
                    Q(user__last_name__icontains=search_text) |
                    Q(user__email__icontains=search_text)
                ).order_by('-id')

            users_data = []
            for profile in profiles:
                user_data = UserSerializer(profile.user).data
                # Add phone number to each user in the list
                user_data["phone_number"] = profile.phone_number

                # Add workload stats for arbitrator or case manager
                if profile.profile_type in ["case_manager", "arbitrator"]:
                    if profile.profile_type == "case_manager":
                        disputes = Dispute.objects.filter(case_manager_rv__id=profile.id)
                        # Filter disputes by client if the current user is a client
                        if is_client:
                            disputes = disputes.filter(client=current_profile)
                    else:  # arbitrator
                        disputes = Dispute.objects.filter(arbitrator_rv__id=profile.id)
                        # Filter disputes by client if the current user is a client
                        if is_client:
                            disputes = disputes.filter(client=current_profile)

                    conciliation_active = disputes.filter(flow_type='conciliation').exclude(status='closed').count()
                    arbitration_active = disputes.filter(flow_type='arbitration').exclude(status='closed').count()
                    closed_cases = disputes.filter(status='closed').count()
                    user_data["workload"] = int(conciliation_active) + int(arbitration_active)
                    user_data["conciliation_active"] = conciliation_active
                    user_data["arbitration_active"] = arbitration_active
                    user_data["closed"] = closed_cases

                # Add total cases for client profiles
                elif profile.profile_type == "client":
                    # Clients should only see their own data
                    if is_client and profile.id != current_profile.id:
                        continue

                    disputes = Dispute.objects.filter(client__id=profile.id)
                    total_cases = disputes.count()
                    user_data["total_cases"] = total_cases

                    conciliation_active = disputes.filter(flow_type='conciliation').exclude(status='closed').count()
                    arbitration_active = disputes.filter(flow_type='arbitration').exclude(status='closed').count()
                    closed_cases = disputes.filter(status='closed').count()
                    user_data["workload"] = int(conciliation_active) + int(arbitration_active)
                    user_data["conciliation_active"] = conciliation_active
                    user_data["arbitration_active"] = arbitration_active
                    user_data["closed"] = closed_cases

                users_data.append(user_data)

            # Apply pagination if requested
            if show_pagination:
                try:
                    paginator = Paginator(users_data, int(page_size))
                    current_page = paginator.page(int(page))

                    return Response({
                        'count': paginator.count,
                        'next': int(page) + 1 if int(page) < paginator.num_pages else None,
                        'previous': int(page) - 1 if int(page) > 1 else None,
                        'total_pages': paginator.num_pages,
                        'results': current_page.object_list
                    })
                except PageNotAnInteger:
                    # If page is not an integer, deliver first page
                    current_page = paginator.page(1)
                    return Response({
                        'count': paginator.count,
                        'next': 2 if paginator.num_pages > 1 else None,
                        'previous': None,
                        'total_pages': paginator.num_pages,
                        'results': current_page.object_list
                    })
                except EmptyPage:
                    # If page is out of range, deliver last page
                    current_page = paginator.page(paginator.num_pages)
                    return Response({
                        'count': paginator.count,
                        'next': None,
                        'previous': paginator.num_pages - 1 if paginator.num_pages > 1 else None,
                        'total_pages': paginator.num_pages,
                        'results': current_page.object_list
                    })
            else:
                # Return without pagination
                return Response(users_data)


    def put(self, request, id):
        """
        Update a user's data (full update).
        """
        user = get_object_or_404(User, id=id)
        data = request.data
        data['password'] = make_password(data.get('password'))

        serializer = UserSerializer(user, data=data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def patch(self, request, id):
        """
        Partially update a user's data.
        """
        user = get_object_or_404(User, id=id)
        data = request.data
        if 'password' in data:
            data['password'] = make_password(data.get('password'))

        serializer = UserSerializer(user, data=data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, id):
        """
        Delete a user by ID.
        """
        user = get_object_or_404(User, id=id)
        profile = get_object_or_404(Profile, user=user)
        profile.delete()
        user.delete()
        return Response({"message": "User and profile deleted successfully"}, status=status.HTTP_204_NO_CONTENT)


# TODO remove this function as we have created cloud run function for the same functionality
class BulkUserDisputeCaseView(APIView):
    parser_classes = (MultiPartParser, FormParser)

    def get(self, request) -> Response:
        """Return list of client emails."""
        # TODO: Fetch actual client emails from the database if needed.
        email_list = ['<EMAIL>']
        return Response({'clients_emails': email_list}, status=status.HTTP_200_OK)

    def post(self, request) -> Response:
        """Handle bulk user and dispute creation."""
        excel_file = request.data.get('excel_file')
        flow_type = request.data.get('flow_type')
        claimant_email = request.data.get('claimant_email')

        if not excel_file or not flow_type:
            return Response({'error': 'Missing required fields: excel_file or flow_type'}, status=status.HTTP_400_BAD_REQUEST)

        # Validate and read Excel file
        df = validate_and_normalize_excel_file(excel_file)

        # Extract common data from the first row of the Excel sheet
        common_data = extract_common_data(df)

        # Process users and create disputes/cases
        created_users, errors = self.process_users_and_create_disputes(df, flow_type, claimant_email, common_data)

        # Prepare response data
        response_data = {
            'message': 'Process completed',
            'total_created_disputes': f'{len(created_users)} disputes created',
            'errors': errors,
        }

        status_code = status.HTTP_207_MULTI_STATUS if errors else status.HTTP_201_CREATED
        return Response(response_data, status=status_code)

    def process_users_and_create_disputes(self, df, flow_type, claimant_email, common_data):
        """Process users from the Excel sheet and create disputes/cases."""
        created_users = []
        errors = []

        claimant_profile = get_object_or_404(Profile, user__email=claimant_email)

        for index, row in df.iterrows():
            email = str(row.get('Borrower\'s Email', '')).strip()
            phone_number = str(row.get('Borrower\'s Number', '')).strip()
            customer_name = str(row.get('Name of the Borrower', '')).strip()
            loan_number = str(row.get('Loan ID', '')).strip()

            # Proper field validation with explicit checks
            missing_fields = []
            if not email or email == 'nan': missing_fields.append("Borrower's Email")
            if not phone_number or phone_number == 'nan': missing_fields.append("Borrower's Number")
            if not customer_name or customer_name == 'nan': missing_fields.append("Name of the Borrower")
            if not loan_number or loan_number == 'nan': missing_fields.append("Loan ID")
            if missing_fields:
                errors.append({
                    'row': index + 2,
                    'error_type': 'missing_fields',
                    'error_detail': f'Missing fields: {", ".join(missing_fields)}',
                    'loan_number': loan_number
                })
                continue

            try:
                with transaction.atomic():
                    # Create dispute and cases for each loan
                    dispute, cases = self.create_dispute_and_cases(
                        claimant_profile=claimant_profile,
                        respondent_name=customer_name,
                        flow_type=flow_type,
                        common_data=common_data,
                        loan_number=loan_number,
                    )
                    created_users.append({
                        'email': email,
                        'phone_number': phone_number,
                        'loan_number': loan_number,
                        'dispute_id': dispute.id,
                        'cases': cases
                    })
            except Exception as e:
                errors.append({
                    'row': index + 2,
                    'email': email,
                    'loan_number': loan_number,
                    'error_type': 'creation_error',
                    'error_detail': str(e)
                })

        return created_users, errors

    def create_dispute_and_cases(self, claimant_profile, respondent_name, flow_type, common_data, loan_number):
        """Create a dispute and its associated cases with loan-specific details."""
        claimant_name = f"{claimant_profile.user.first_name} {claimant_profile.user.last_name}"
        formatted_name = f"{claimant_name} vs {respondent_name}"

        # Create dispute with loan-specific details
        profile_instance = Profile.objects.get(user=self.request.user)
        dispute = Dispute.objects.create(
            name=formatted_name,
            flow_type=flow_type,
            order_date=timezone.now().date(),
            description=formatted_name,
            company=claimant_profile.company,
            loan_id=loan_number,
            created_by=profile_instance,
            claimant=claimant_profile
            # **common_data  # Add additional fields from common data if necessary.
        )        
        return dispute, [dispute]


class TriggerBulkDisputeCampaignView(APIView):
    permission_classes = [IsAuthenticated, IsAdminOrClient]
    parser_classes = (MultiPartParser, FormParser)

    def post(self, request) -> Response:
        """Trigger bulk processing campaign"""
        excel_file = request.data.get('excel_file')
        flow_type = request.data.get('flow_type')
        claimant_email = request.data.get('claimant_email')
        client_email = request.data.get('client_email')
        has_co_borrowers = request.data.get('has_co_borrowers', False)
        campaign_id = request.data.get('campaign_id')

        user=request.user
        current_profile = get_object_or_404(Profile,user=user)
        profile_id = current_profile.id

        try:
            total_rows = 0
            file_to_upload = None

            if campaign_id:
                #this is the case where disputes were already created for this campaign but more cases have been added to the xls.
                #only excel file and campaign id will be sent
                campaign = Campaign.objects.get(id=campaign_id)
                if not campaign:
                    return Response({'error':'Incorrect campaign Id'},status=400)
                
                if not excel_file:
                    return Response({'error': 'Missing file'}, status=400)   
                            
                if campaign.excel_file_name.split('/')[-1] != excel_file.name:                    
                    return Response({'error': 'Excel file name is different from the previous file uploaded.'}, status=400)     
                
                if not validate_profile_campaign(current_profile,campaign):
                    return Response({'error': 'You do not have permission to access this campaign'},
                               status=status.HTTP_403_FORBIDDEN) 
                
                client = campaign.client               
                sample_dispute = Dispute.objects.filter(campaign_id=campaign_id).first()
                claimant_email = sample_dispute.claimant.user.email
                client_email = client.user.email
                flow_type = campaign.flow_type
                has_co_borrowers = campaign.has_co_borrowers
                campaign.case_creation_status = 'processing'
                campaign.save()

                # For existing campaigns, we still need to validate/normalize the new file
                df, normalized_excel_file = validate_and_normalize_excel_file(excel_file)
                total_rows = len(df)
                file_to_upload = normalized_excel_file

                print(f'client email is {client_email} and claimant email {claimant_email}')
            else:
                # New campaign
                if not all([excel_file, flow_type, claimant_email]):
                    return Response({'error': 'Missing required fields'}, status=400)

                client = Profile.objects.get(user__email=client_email)
                if not client:
                    return Response({'error': 'Client not found'}, status=400)

                claimant = Profile.objects.get(user__email=claimant_email)
                if not claimant:
                    return Response({'error': 'Claimant not found'}, status=400)

                if claimant.parent_client != client:
                    return Response({'error': 'Claimant is not associated with the client'}, status=400)

                if current_profile.profile_type == ProfileType.arbitrator.name:
                    return Response({'error': 'You do not have permission to create disputes'},
                               status=status.HTTP_403_FORBIDDEN) 

                # Validate and normalize Excel file
                df, normalized_excel_file = validate_and_normalize_excel_file(excel_file)
                total_rows = len(df)
                file_to_upload = normalized_excel_file

                data = dict(flow_type=flow_type,
                        has_co_borrowers=has_co_borrowers,
                        total_rows=total_rows,
                        number_of_cases_created=0,
                        created_by=request.user,
                        client=client)
                
                # Create campaign record
                campaign = Campaign.objects.create(**data)
                campaign.save()

            excel_file_name = excel_file.name
            excel_file_path = f"notices/{campaign.id}/{excel_file_name}"

            # Upload the file (ensure we have valid content to upload)
            if file_to_upload is None:
                return Response({'error': 'Failed to process Excel file'}, status=400)

            excel_file.seek(0)  # Reset file pointer to beginning
            gcs_manager.upload_file(excel_file_path, file_to_upload)

            campaign.excel_file_name = excel_file_path
            campaign.excel_file_uploaded = True
            campaign.save()

            # Publish to Pub/Sub
            credentials = service_account.Credentials.from_service_account_info(CREDENTIALS_DICT)
            publisher = pubsub_v1.PublisherClient(credentials=credentials)
            topic_path = publisher.topic_path(GCP_PROJECT_ID, PUBSUB_BULK_CASE_CREATION)

            message = {
                'campaign_id': campaign.id,
                'file_path': excel_file_path,
                'flow_type': flow_type,
                'claimant_email': claimant_email,
                'client_email': client_email,
                'user_id': request.user.id,
                'profile_id': profile_id,
                'has_co_borrowers': has_co_borrowers
            }

            future = publisher.publish(topic_path, json.dumps(message).encode('utf-8'))
            future.result()  # Wait for publish

            return Response({
                'message': 'Campaign processing started',
                'campaign_id': campaign.id,
                'total_rows': total_rows
            }, status=status.HTTP_202_ACCEPTED)

        except Exception as e:
            return Response({'error': str(e)}, status=500)


class DownloadBulkDisputeCreationReportView(APIView):
    permission_classes = [IsAuthenticated, IsAdminOrClient]

    def get(self, request, campaign_id=None):
        if not campaign_id:
            return Response({'error': 'campaign_id is required'}, status=status.HTTP_400_BAD_REQUEST)
        user=request.user
        current_profile = get_object_or_404(Profile,user=user)
        campaign = get_object_or_404(Campaign,id=campaign_id)
        if not validate_profile_campaign(current_profile,campaign):
            return Response({'error': 'You do not have permission to access this campaign'},
                               status=status.HTTP_403_FORBIDDEN) 

        folder_path = f"notices/{campaign_id}/case-creation-reports/"
        try:
            blob_list = gcs_manager.list_files(prefix=folder_path)
            files = []
            for blob_name in blob_list:
                if blob_name.endswith('/'):
                    continue

                signed_url = gcs_manager.generate_signed_url(blob_name)
                files.append({
                    'file_name': blob_name.split('/')[-1],
                    'url': signed_url
                })
            if not files:
                return Response({'error': 'No files found in reports folder.'}, status=404)
            return Response({'files': files}, status=200)
        except Exception as e:
            return Response({'error': f'Error retrieving report files: {str(e)}'}, status=500)


def campaign_progress_stream(request, campaign_id):
    def event_stream():
        last_update = None
        while True:
            campaign = Campaign.objects.get(id=campaign_id)
            current_state = {
                'processed': campaign.processed_rows,
                'total': campaign.total_rows,
                'status': campaign.case_creation_status,
                'errors': campaign.processing_errors
            }
            if current_state != last_update:
                yield f"data: {json.dumps(current_state)}\n\n"
                last_update = current_state
            if campaign.case_creation_status in ['completed', 'failed']:
                break
            time.sleep(1)  # Check every second
    return StreamingHttpResponse(event_stream(), content_type='text/event-stream')


class CampaignListView(APIView):
    permission_classes = [IsAuthenticated, IsAdminOrClient]

    def get(self, request):
        client_id = request.query_params.get('client_id') #id is the user id not profile
        user = request.user
        current_profile = Profile.objects.get(user=user)
        current_profile_type = current_profile.profile_type

        # If client_id is provided in the URL, filter by that client
        if client_id:
            campaigns = Campaign.objects.filter(client__user__id=client_id)            
        elif current_profile_type == ProfileType.client.name:
            campaigns = Campaign.objects.filter(client__user=user)
        if current_profile_type == ProfileType.admin.name:
            campaigns = Campaign.objects.all()
        elif current_profile_type == ProfileType.sub_client.name:            
            client = Profile.objects.get(id=current_profile.parent_client.id)            
            campaigns = Campaign.objects.filter(client=client)  
        elif current_profile_type == ProfileType.case_manager.name:
            campaign_ids = Dispute.objects.filter(case_manager_rv=current_profile).values_list('campaign__id',flat=True).distinct()
            campaigns = Campaign.objects.filter(id__in=campaign_ids)
        elif current_profile_type == ProfileType.arbitrator.name:
            campaign_ids = Dispute.objects.filter(arbitrator_rv=current_profile).values_list('campaign__id',flat=True).distinct()
            campaigns = Campaign.objects.filter(id__in=campaign_ids)

        show_pagination = request.query_params.get('show_pagination', 'false').lower() == 'true'
        if show_pagination:
            paginator = PageNumberPagination()
            page_size = request.query_params.get('page_size', 25)
            paginator.page_size = int(page_size)
            result_page = paginator.paginate_queryset(campaigns, request)

            serializer = CampaignSerializer(result_page, many=True)
            return paginator.get_paginated_response(serializer.data)
        else:
            serializer = CampaignSerializer(campaigns, many=True)
            return Response(serializer.data)


class CaseFileListView(APIView):
    permission_classes = [IsAuthenticated, IsAdminOrClient]
    
    # Thread-local storage for database connections
    _thread_local = threading.local()
    
    def get_db_connection(self):
        """Get thread-safe database connection"""
        if not hasattr(self._thread_local, 'connection'):
            self._thread_local.connection = connections['default']
        return self._thread_local.connection

    def get(self, request):
        start_time = time.time()        
        
        loan_id = request.query_params.get('loan_id')
        case_id = request.query_params.get('case_id')
        client_id = request.query_params.get('client_id') #client user id
        case_manager_id = request.query_params.get('case_manager_id') #case manager user id
        client_name= request.query_params.get('client')
        claimant_name = request.query_params.get('claimant')
        respondent = request.query_params.get('respondent')        
        page_size = request.query_params.get('page_size')
        arbitrator_name= request.query_params.get('arbitrator')
        arbitrator_id = request.query_params.get('arbitrator_id') #arbitrator user_id
        export_format = request.query_params.get('export')
        campaign_id = request.query_params.get('campaign_id')
        dispute_status = request.query_params.get('status')
        allowed_params = ['loan_id','case_id','client_id','case_manager_id','client','claimant','respondent','page_size','arbitrator','arbitrator_id','export','campaign_id','status','page']

        if arbitrator_id:
            if not arbitrator_id.isnumeric():
                return Response('Arbitrator id must be a number',status=status.HTTP_400_BAD_REQUEST)
            if arbitrator_name:
                arbitrator = User.objects.get(id=arbitrator_id)
                if arbitrator.first_name == arbitrator_name:
                    arbitrator_name = None           #we will go ahead using only the id for filter condition
                else:
                    return Response('Arbitrator id does not correspond to the name given',status=status.HTTP_400_BAD_REQUEST)
        
        if client_id:
            if not client_id.isnumeric():
                return Response('Client id must be a number',status=status.HTTP_400_BAD_REQUEST)
            if client_name:            
                client = User.objects.get(id=client_id)
                if client.first_name == client_name:
                    client_name = None           #we will go ahead using only the id for filter condition
                else:
                    return Response('Client id does not correspond to the name given',status=status.HTTP_400_BAD_REQUEST)
            
        complete_query_string = request.META.get('QUERY_STRING', '')
        
        params = complete_query_string.split('&')
        for param in params:
            if param.split('=')[0] not in allowed_params:
                return Response('Invalid parameter',status=status.HTTP_400_BAD_REQUEST)

        user = request.user
        
        current_user_profile = Profile.objects.get(user=user)
        current_user_profile_type = current_user_profile.profile_type


        #validations for all parameters
        if loan_id and (not validate_profile_loan_id(current_user_profile,loan_id)):    
                return Response({'error':'You are not authorized to view this information'},status=status.HTTP_403_FORBIDDEN)
        
        if case_id:
            dispute = get_object_or_404(Dispute,id=case_id)
            if not validate_profile_dispute(current_user_profile,dispute):
                return Response({'error':'You are not authorized to view this information'},status=status.HTTP_403_FORBIDDEN)
            
        if client_id:
            client = get_object_or_404(Profile,user_id=client_id)
            if not validate_profile_client(current_user_profile,client):
                return Response({'error':'You are not authorized to view this information'},status=status.HTTP_403_FORBIDDEN)
            
        if case_manager_id:
            case_manager = get_object_or_404(Profile,user_id=case_manager_id)
            if not validate_profile_case_manager(current_user_profile,case_manager):
                return Response({'error':'You are not authorized to view this information'},status=status.HTTP_403_FORBIDDEN)
            
        if campaign_id:
            campaign = get_object_or_404(Campaign,id=campaign_id)
            if not validate_profile_campaign(current_user_profile,campaign):
                return Response({'error':'You are not authorized to view this information'},status=status.HTTP_403_FORBIDDEN)
            
        if arbitrator_id:
            arbitrator = get_object_or_404(Profile,user_id=arbitrator_id)
            if not validate_profile_arbitrator(current_user_profile,arbitrator):
                return Response({'error':'You are not authorized to view this information'},status=status.HTTP_403_FORBIDDEN)
            
        disputes_filter_conditions = dict() #By default AND operation occurs with filter conditions        
        
        if current_user_profile_type == ProfileType.admin.name:
                pass
        elif current_user_profile_type ==  ProfileType.client.name:
                disputes_filter_conditions['client'] = current_user_profile
        elif current_user_profile_type == ProfileType.sub_client.name:
                client_profile= Profile.objects.get(id=current_user_profile.parent_client.id)
                disputes_filter_conditions['client'] = client_profile
        elif current_user_profile_type == ProfileType.case_manager.name:
                disputes_filter_conditions['case_manager_rv'] = current_user_profile
        elif current_user_profile_type == ProfileType.arbitrator.name:
                disputes_filter_conditions['arbitrator_rv'] = current_user_profile
        
    
        if not page_size:
            page_size = 25
 
        if loan_id:                
            disputes_filter_conditions['loan_id__contains'] = loan_id

        if case_id:
            if not case_id.isnumeric():
                return Response('case id must be a number',status=status.HTTP_400_BAD_REQUEST)
            disputes_filter_conditions['id'] = case_id

        if campaign_id:
            if not campaign_id.isnumeric():
                return Response('Campaign id must be a number',status=status.HTTP_400_BAD_REQUEST)
            disputes_filter_conditions['campaign_id'] = campaign_id

        if client_id:
            #this is the user id of client.             
            disputes_filter_conditions['client__user__id'] = client_id

        if case_manager_id:
            if not case_manager_id.isnumeric():
                return Response('Case Manager id must be a number',status=status.HTTP_400_BAD_REQUEST)
            
            #this is the user id of case manager. Get its profile and use it to get disputes.
            cm_profile_id = Profile.objects.get(user_id=case_manager_id).id
            disputes_filter_conditions['case_manager_rv__id'] = cm_profile_id

        if client_name:
            disputes_filter_conditions['client__user__first_name__contains']=client_name  #user id is being sent not profile id

        if claimant_name:
            disputes_filter_conditions['claimant__user__first_name__contains'] = claimant_name

        if respondent:
            disputes_filter_conditions['respondents_name__contains']=respondent   

        if arbitrator_id:
            #this is the user id of arbitrator. Get its profile and use it to get disputes.
            arbitrator_profile_id = Profile.objects.get(user_id=arbitrator_id).id
            disputes_filter_conditions['arbitrator_rv__id'] = arbitrator_profile_id

        if arbitrator_name:
            disputes_filter_conditions['arbitrator_rv__user__first_name__contains']=arbitrator_name 

        if dispute_status:
            disputes_filter_conditions['status'] = dispute_status

        case_files = Dispute.objects.filter(**disputes_filter_conditions).select_related(
            'client__user',
            'campaign'
        ).all().order_by('-created')       

        # Excel/CSV export with multithreading
        if export_format and export_format.lower() in ['excel', 'csv']:
            print(f"Query execution time: {time.time() - start_time:.2f} seconds")
            if export_format.lower() == 'excel':
                return self.export_to_excel(case_files)
            else:
                return self.export_to_csv(case_files)

        # Normal JSON response
        paginator = PageNumberPagination()
        paginator.page_size = page_size
        paginator.page_query_param = 'page'

        queryset = paginator.paginate_queryset(queryset=case_files, request=request)
        serializer = CaseFileListSerializer(queryset, many=True)
        
        return paginator.get_paginated_response(serializer.data)    

    def prepare_case_files_data_parallel(self, case_files, chunk_size=500, max_workers=4):
        """
        OPTIMIZATION 5: Multithreaded data preparation for large datasets
        """
        print(f"Processing {case_files.count()} records with {max_workers} threads...")
        
        # Convert queryset to list to avoid database connection issues in threads
        case_files_list = list(case_files)
        total_records = len(case_files_list)
        
        if total_records == 0:
            return ['Case ID', 'Loan ID', 'Client Name', 'Claimant', 'Respondent', 'Arbitrator Name', 'Status'], []
        
        # Split data into chunks for parallel processing
        chunks = [case_files_list[i:i + chunk_size] for i in range(0, total_records, chunk_size)]
        
        headers = ['Case ID', 'Loan ID', 'Client Name', 'Claimant', 'Respondent', 'Arbitrator Name', 'Status']
        all_data = []
        
        def process_chunk(chunk):
            """Process a chunk of case files"""
            chunk_data = []
            for case_file in chunk:
                try:
                    case_id = str(case_file.id)
                    loan_id = str(case_file.loan_id) if case_file.loan_id else "N/A"

                    # Client Name - optimized access
                    client_name = "N/A"
                    if hasattr(case_file, 'client') and case_file.client and hasattr(case_file.client, 'user') and case_file.client.user:
                        first_name = case_file.client.user.first_name or ""
                        last_name = case_file.client.user.last_name or ""
                        client_name = f"{first_name} {last_name}".strip() or "N/A"

                    # Claimant and Respondent - optimized
                    if hasattr(case_file,'claimant'):
                        claimant = f"{case_file.claimant.user.first_name} {case_file.claimant.user.last_name}".strip() 

                    if hasattr(case_file,'respondents_name'):
                        respondent = f"{case_file.respondents_name}".strip()                     

                    # Arbitrator Name - optimized
                    arbitrator_name = "N/A"
                    if hasattr(case_file, 'arbitrator_rv'):
                        arbitrators = list(case_file.arbitrator_rv.all())  # Already prefetched
                        if arbitrators and hasattr(arbitrators[0], 'user') and arbitrators[0].user:
                            first_name = arbitrators[0].user.first_name or ""
                            last_name = arbitrators[0].user.last_name or ""
                            arbitrator_name = f"{first_name} {last_name}".strip() or "N/A"

                    # Status - get from the dispute object
                    status_value = case_file.status if case_file.status else "N/A"

                    chunk_data.append([case_id, loan_id, client_name, claimant, respondent, arbitrator_name, status_value])
                except Exception as e:
                    print(f"Error processing case file {getattr(case_file, 'id', 'unknown')}: {str(e)}")
                    # Add error row to maintain data integrity
                    chunk_data.append([str(getattr(case_file, 'id', 'Error')), "Error", "Error", "Error", "Error", "Error", "Error"])
            
            return chunk_data

        # Use ThreadPoolExecutor for parallel processing
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all chunks for processing
            future_to_chunk = {executor.submit(process_chunk, chunk): i for i, chunk in enumerate(chunks)}
            
            # Collect results in order
            chunk_results = [None] * len(chunks)
            for future in as_completed(future_to_chunk):
                chunk_index = future_to_chunk[future]
                try:
                    chunk_results[chunk_index] = future.result()
                except Exception as e:
                    print(f"Error in chunk {chunk_index}: {str(e)}")
                    chunk_results[chunk_index] = []
        
        # Combine all chunk results
        for chunk_data in chunk_results:
            if chunk_data:
                all_data.extend(chunk_data)
        
        print(f"Processed {len(all_data)} records successfully")
        return headers, all_data

    def prepare_case_files_data(self, case_files):
        total_count = case_files.count()

        # Use parallel processing for datasets larger than 100 records
        if total_count > 100:
            return self.prepare_case_files_data_parallel(case_files, chunk_size=min(500, total_count // 4), max_workers=4)

        # Use original method for small datasets
        headers = ['Case ID', 'Loan ID', 'Client Name', 'Claimant', 'Respondent', 'Arbitrator Name', 'Status']
        data = []

        for case_file in case_files:
            case_id = str(case_file.id)
            loan_id = str(case_file.loan_id) if case_file.loan_id else "N/A"

            # Client Name
            client_name = "N/A"
            client = getattr(case_file, 'client', None)
            if client and getattr(client, 'user', None):
                first_name = client.user.first_name or ""
                last_name = client.user.last_name or ""
                client_name = f"{first_name} {last_name}".strip() or "N/A"

            # Claimant and Respondent
            claimant = "N/A"
            if hasattr(case_file,'claimant'):
                claimant = f"{case_file.claimant.user.first_name} {case_file.claimant.user.last_name}".strip()

            respondent = "N/A"
            if hasattr(case_file,'respondents_name'):
                respondent = case_file.respondents_name

            # Arbitrator Name
            arbitrator_name = "N/A"
            arbitrator = case_file.arbitrator_rv.first()
            if arbitrator and getattr(arbitrator, 'user', None):
                first_name = arbitrator.user.first_name or ""
                last_name = arbitrator.user.last_name or ""
                arbitrator_name = f"{first_name} {last_name}".strip() or "N/A"

            # Status - get from the dispute object
            status_value = case_file.status if case_file.status else "N/A"

            data.append([case_id, loan_id, client_name, claimant, respondent, arbitrator_name, status_value])

        return headers, data

    def export_to_csv(self, case_files):
        """Export case files to CSV format with performance optimization."""
        try:
            start_time = time.time()
            count = case_files.count()
            print(f"Starting CSV export for {count} case files...")

            # Check if there's no data to export
            if count == 0:
                # Ensure Response is imported at the top of the function
                from rest_framework.response import Response
                return Response({"error": "No data available to export"}, status=404)

            headers, data = self.prepare_case_files_data(case_files)

            # Double-check if data is empty after preparation
            if not data:
                # Import Response here too in case it wasn't imported earlier
                from rest_framework.response import Response
                return Response({"error": "No data available to export"}, status=404)

            response = HttpResponse(content_type='text/csv')
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'case_files_export_{timestamp}.csv'
            response['Content-Disposition'] = f'attachment; filename="{filename}"'

            writer = csv.writer(response)
            writer.writerow(headers)

            # OPTIMIZATION 7: Write data in batches for better performance
            batch_size = 1000
            for i in range(0, len(data), batch_size):
                batch = data[i:i + batch_size]
                writer.writerows(batch)

            print(f"CSV export completed in {time.time() - start_time:.2f} seconds!")
            return response
        except Exception as e:
            print(f"CSV export error: {str(e)}")
            # Ensure Response is imported in the exception handler
            from rest_framework.response import Response
            return Response({"error": f"CSV export failed: {str(e)}"}, status=500)

    def export_to_excel(self, case_files):
        """Export case files to Excel format (.xlsx) with performance optimization."""
        try:
            start_time = time.time()
            count = case_files.count()
            print(f"Starting Excel export for {count} case files...")

            # Check if there's no data to export
            if count == 0:
                return Response({"error": "No data available to export"}, status=404)

            headers, data = self.prepare_case_files_data(case_files)

            # Double-check if data is empty after preparation
            if not data:
                return Response({"error": "No data available to export"}, status=404)

            # OPTIMIZATION 8: Use pandas more efficiently for large datasets
            df = pd.DataFrame(data, columns=headers)

            output = BytesIO()
            # Fix: Remove 'options' parameter and handle constant_memory differently
            with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
                # Apply constant memory option directly to the workbook if needed
                if hasattr(writer, 'book') and hasattr(writer.book, 'use_constant_memory'):
                    writer.book.use_constant_memory = True
                df.to_excel(writer, index=False, sheet_name='Case Files')

            output.seek(0)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'case_files_export_{timestamp}.xlsx'

            response = HttpResponse(
                output,
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            response['Content-Disposition'] = f'attachment; filename="{filename}"'

            print(f"Excel export completed in {time.time() - start_time:.2f} seconds!")
            return response
        except Exception as e:
            print(f"Excel export error: {str(e)}")
            return Response({"error": f"Excel export failed: {str(e)}"}, status=500)


class CaseFileDetailView(APIView):
    permission_classes = [IsAuthenticated, IsAnyRole]

    def get(self, request, pk):
        dispute = get_object_or_404(Dispute, pk=pk)

        # Check user's profile type
        user = request.user
        current_profile = get_object_or_404(Profile,user=user)
        
        if not validate_profile_dispute(current_profile,dispute):
            return Response({'error': 'You do not have permission to access this dispute'},
                               status=status.HTTP_403_FORBIDDEN) 

        client_profile = dispute.client if dispute and dispute.client else None
        case_managers = dispute.case_manager_rv.all()
        arbitrators = dispute.arbitrator_rv.all()


        client_data = ProfileInfoSerializer(client_profile).data if client_profile else {}
        case_managers_data = ProfileInfoSerializer(case_managers, many=True).data

        # Add arbitrator assignment date to each arbitrator's data
        arbitrators_data = []
        for arbitrator in arbitrators:
            arbitrator_data = ProfileInfoSerializer(arbitrator).data
            # Add the arbitrator assigned date from the campaign
            if dispute.campaign and dispute.campaign.assigned_arbitrators_at:
                arbitrator_data['assigned_arbitrators_at'] = dispute.campaign.assigned_arbitrators_at
            arbitrators_data.append(arbitrator_data)

        # Create borrowers data from respondent information
        borrowers_data = []
        if dispute.respondents_name or dispute.respondents_email:
            borrower = {
                'name': dispute.respondents_name or '',
                'email': dispute.respondents_email or ''
            }
            borrowers_data.append(borrower)

        notices = dispute.notice_set.all().order_by('created_at')

        # Build the timeline dict
        notice_timeline = {
            notice.id: notice.created_at
            for notice in notices
        }

        #Fetch basic information of dispute
        dispute_details = {'name':dispute.name, 'created_on':dispute.created, 'flow_type': dispute.flow_type,'loan_id':dispute.loan_id,'campaign':dispute.campaign.excel_file_name.split('/')[-1],'status':dispute.status,'closure_reason':"" if dispute.status != 'closed' else dispute.closed_reason,'closed_date':dispute.closed_date}

        return Response({
            "client": client_data,
            "case_managers": case_managers_data,
            "arbitrators": arbitrators_data,
            "borrowers": borrowers_data,
            "notice_timeline": notice_timeline,
            "dispute":dispute_details
        })


class ClientProfileListView(APIView):
    permission_classes = [IsAuthenticated, IsAdminOrClient]

    def get(self, request):
        # Check if user is client
        user = request.user
        current_profile = Profile.objects.get(user=user)
        current_profile_type = current_profile.profile_type

        clients = Profile.objects.filter(profile_type="client")

        if current_profile_type == ProfileType.admin.name:
            pass
        elif current_profile_type == ProfileType.client.name:          
            clients = clients.filter(id=current_profile.id)
        elif current_profile_type == ProfileType.sub_client.name:
            client_profile = Profile.objects.get(id=current_profile.parent_client.id)
            clients = clients.filter(id=client_profile.id)
        elif current_profile_type == ProfileType.case_manager.name:
            client_ids = Dispute.objects.filter(case_manager_rv__id = current_profile.id).values_list('client',flat=True).distinct()
            clients = clients.filter(id__in = client_ids)
        elif current_profile_type == ProfileType.arbitrator.name:
            client_ids = Dispute.objects.filter(arbitrator_rv__id = current_profile.id).values_list('client',flat=True).distinct()
            clients = clients.filter(id__in = client_ids)
        serializer = ProfileMajorDetailSerializer(clients, many=True)
        return Response(serializer.data)


class ClaimantProfileListView(APIView):
    permission_classes = [IsAuthenticated, IsAdminOrClient]

    def get(self, request):
        # Check if user is client
        user = request.user
        current_profile = Profile.objects.get(user=user)
        current_profile_type = current_profile.profile_type

        claimants = Profile.objects.filter(profile_type="claimant")

        if current_profile_type == ProfileType.admin.name:
            pass
        elif current_profile_type == ProfileType.client.name:
            claimants = claimants.filter(parent_client=current_profile)
        elif current_profile_type == ProfileType.sub_client.name:
            client_profile = Profile.objects.get(id=current_profile.parent_client.id)
            claimants = claimants.filter(parent_client=client_profile)
        elif current_profile_type == ProfileType.case_manager.name:
            client_ids = Dispute.objects.filter(case_manager_rv__id = current_profile.id).values_list('client',flat=True).distinct()
            claimants = claimants.filter(parent_client__id__in = client_ids)
        elif current_profile_type == ProfileType.arbitrator.name:
            client_ids = Dispute.objects.filter(arbitrator_rv__id = current_profile.id).values_list('client',flat=True).distinct()
            claimants = claimants.filter(parent_client__id__in = client_ids)

        serializer = ProfileMajorDetailSerializer(claimants, many=True)
        return Response(serializer.data)


class KeyMetricsView(APIView):
    permission_classes = [IsAuthenticated, IsAnyRole]

    def get(self, request):
        now = timezone.now()

        # Check user's profile type
        user = request.user
        current_user_profile = Profile.objects.get(user=user)
        current_profile_type = current_user_profile.profile_type               

        # Start of the current month
        start_of_month = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

        # Start of the current week (Monday)
        start_of_week = now - timedelta(days=now.weekday())
        start_of_week = start_of_week.replace(hour=0, minute=0, second=0, microsecond=0)
        dispute_filter_conditions = dict()
        # Count for current week        
        entry_conditions = dict()   

        if current_profile_type == ProfileType.admin.name:
            entry_conditions['timestamp__gte'] = start_of_week
        elif current_profile_type == ProfileType.client.name:
            dispute_filter_conditions['client'] = current_user_profile

            # Get all loan_ids from disputes associated with this client
            loan_ids = Dispute.objects.filter(client=current_user_profile).values_list('loan_id', flat=True)
            # Filter entries by these loan_ids
            entry_conditions['loan_id__in'] = loan_ids 
            entry_conditions['timestamp__gte'] = start_of_week  
        elif current_profile_type == ProfileType.sub_client.name:
            client = Profile.objects.get(id=current_user_profile.parent_client.id)    

            dispute_filter_conditions['client'] = client        

            loan_ids = Dispute.objects.filter(client=client).values_list('loan_id', flat=True)
            # Filter entries by these loan_ids
            entry_conditions['loan_id__in'] = loan_ids 
            entry_conditions['timestamp__gte'] = start_of_week      
        elif current_profile_type == ProfileType.case_manager.name:
            dispute_filter_conditions['case_manager_rv'] = current_user_profile

            loan_ids = Dispute.objects.filter(case_manager_rv=current_user_profile).values_list('loan_id', flat=True)
            entry_conditions['loan_id__in'] = loan_ids     
            entry_conditions['timestamp__gte'] = start_of_week
        elif current_profile_type == ProfileType.arbitrator.name:
            dispute_filter_conditions['arbitrator_rv'] = current_user_profile

            loan_ids = Dispute.objects.filter(arbitrator_rv=current_user_profile).values_list('loan_id', flat=True)
            entry_conditions['loan_id__in'] = loan_ids     
            entry_conditions['timestamp__gte'] = start_of_week
        

        dispute_filter_conditions['created__gte'] = start_of_month

        disputes_this_month = Dispute.objects.filter(**dispute_filter_conditions).count()                 
        
        if entry_conditions:
            #we have to take distinct message and phone number because one message can go to different numbers(like for co-borrowers)
            # and phone number can get different messages.
            entries_this_week = Entry.objects.filter(**entry_conditions).values_list('msg_id','phone_number').distinct().count()
        else:
            entries_this_week=0

        return Response({
            "total_cases_this_month": disputes_this_month,
            "notices_sent_this_week": entries_this_week,
        })

class DisputeNotices(APIView):
    permission_classes = [IsAuthenticated, IsAdminOrClient]

    def get(self, request, pk):
        dispute = get_object_or_404(Dispute, pk=pk)

        # Check user's profile type
        user = request.user
        current_profile = get_object_or_404(Profile,user=user)
        if not validate_profile_dispute(current_profile,dispute):
                return Response({'error': 'You do not have permission to access this dispute'},
                               status=status.HTTP_403_FORBIDDEN)

        notices = Notice.objects.filter(dispute=dispute)
        serializer = NoticeSerializer(notices, many=True)
        return Response(serializer.data)


class NoticesDownloadById(APIView):
    permission_classes = [IsAuthenticated, IsAdminOrClient]

    def get(self, request, pk):
        notice = get_object_or_404(Notice, pk=pk)

        # Check user's profile type
        user = request.user
        current_profile = get_object_or_404(Profile,user=user)
        
        if not validate_profile_notice(current_profile,notice):
            return Response({'error': 'You do not have permission to access this notice'},
                               status=status.HTTP_403_FORBIDDEN) 

        if not notice.file_path:
            return Response({"error":"Notice PDF is not available"})
        notice_signed_url = gcs_manager.generate_signed_url(notice.file_path)

        return Response({"notice_signed_url":notice_signed_url})

class GenericProfilesView(APIView):
    permission_classes = [IsAuthenticated, IsAdminOrClient]

    def get(self, request, pk=None):
        profile_type = request.query_params.get('profile_type')
        search_text = request.query_params.get('search_text', '').strip()
        use_pagination = request.query_params.get('pagination', 'false').lower() == 'true'
        
        # Check user's profile type
        user = request.user
        current_profile = Profile.objects.get(user=user)
        current_profile_type = current_profile.profile_type

        profiles = Profile.objects.filter(profile_type=profile_type)

        # Filter profiles based on user's profile type
        if current_profile_type == ProfileType.admin.name:
            # Admin can see all profiles
            pass
        elif current_profile_type == ProfileType.client.name:
            # Client and sub_client can only see their own profile if requesting client profiles
            if profile_type == "client":           
                profiles = profiles.filter(id=current_profile.id,profile_type=ProfileType.client.name)
            elif profile_type == "sub_client":
                profiles=profiles.filter(id=current_profile.parent_client.id,profile_type=ProfileType.sub_client.name)
            elif profile_type == "arbitrator":
                # For case managers and arbitrators, only show those assigned to client's disputes                
                disputes = Dispute.objects.filter(client=current_profile)                
                arbitrator_ids = disputes.values_list('arbitrator_rv__id', flat=True).distinct()
                profiles = profiles.filter(id__in=arbitrator_ids, profile_type = ProfileType.arbitrator.name)
            elif profile_type == "case_manager":
                if profile_type == "case_manager":
                    # Get unique case manager IDs from client's disputes
                    disputes = Dispute.objects.filter(client=current_profile)                
                    case_manager_ids = disputes.values_list('case_manager_rv__id', flat=True).distinct()                    
                    profiles = profiles.filter(id__in=case_manager_ids,profile_type=ProfileType.case_manager.name)
        elif current_profile_type == ProfileType.sub_client.name:          
            if profile_type == "client":           
                profiles = profiles.filter(id=current_profile.parent_client.id,profile_type=ProfileType.client.name)
            elif profile_type == "sub_client":
                client_profile = current_profile.parent_client
                profiles = Profile.objects.filter(parent_client=client_profile,profile_type=ProfileType.sub_client.name)
            elif profile_type == "arbitrator":
                # For case managers and arbitrators, only show those assigned to client's disputes      
                client_profile = current_profile.parent_client          
                disputes = Dispute.objects.filter(client=client_profile)                
                arbitrator_ids = disputes.values_list('arbitrator_rv__id', flat=True).distinct()
                profiles = profiles.filter(id__in=arbitrator_ids, profile_type = ProfileType.arbitrator.name)
            elif profile_type == "case_manager":
                if profile_type == "case_manager":
                    # Get unique case manager IDs from client's disputes
                    client_profile = current_profile.parent_client     
                    disputes = Dispute.objects.filter(client=client_profile)                
                    case_manager_ids = disputes.values_list('case_manager_rv__id', flat=True).distinct()                    
                    profiles = profiles.filter(id__in=case_manager_ids,profile_type=ProfileType.case_manager.name)
        elif current_profile_type == ProfileType.case_manager.name:
            # Case manager can only see themselves if requesting case manager profiles
            if profile_type == "case_manager":                
                profiles = profiles.filter(id=current_profile.id)
        elif current_profile_type == ProfileType.arbitrator.name:
            # Arbitrator can only see themselves if requesting arbitrator profiles
            if profile_type == "arbitrator":                
                profiles = profiles.filter(id=current_profile.id)

        if search_text:
            profiles = profiles.filter(
                Q(user__first_name__icontains=search_text) |
                Q(user__last_name__icontains=search_text) |
                Q(user__email__icontains=search_text) |
                Q(phone_number__icontains=search_text) |
                Q(region__icontains=search_text) |
                Q(city__icontains=search_text)
            )

        if use_pagination:
            paginator = PageNumberPagination()
            paginator.page_size = 25  # Or set from request.query_params.get('page_size')
            result_page = paginator.paginate_queryset(profiles, request)
            data = []

            for profile in result_page:
                serialized = ProfileInfoSerializer(profile).data

                if profile_type in ['case_manager', 'arbitrator']:
                    if profile_type == 'case_manager':
                        workload = Dispute.objects.filter(case_manager_rv=profile).count()
                    elif profile_type == 'arbitrator':
                        workload = Dispute.objects.filter(arbitrator_rv=profile).count()

                    serialized['workload'] = workload

                data.append(serialized)

            return paginator.get_paginated_response(data)

        else:
            data = []
            for profile in profiles:
                serialized = ProfileInfoSerializer(profile).data

                if profile_type in ['case_manager', 'arbitrator']:
                    if profile_type == 'case_manager':
                        workload = Dispute.objects.filter(case_manager_rv=profile).count()
                    elif profile_type == 'arbitrator':
                        workload = Dispute.objects.filter(arbitrator_rv=profile).count()

                    serialized['workload'] = workload

                data.append(serialized)

            return Response({
                "count": profiles.count(),
                "results": data
            })


def send_emails_in_batch(emails_to_send):
    for email_func, args in emails_to_send:
        email_func(*args)



class AssignProfessionalsView(APIView):
    permission_classes = [IsAuthenticated,]

    def post(self, request):
        professional_type = request.data.get("professional_type")
        assignments = request.data.get("assignments")
        campaign_id = request.data.get("campaign_id")

        # Check user's profile type for RBAC
        user = request.user
        current_profile = Profile.objects.get(user=user)
        current_profile_type = current_profile.profile_type

        is_admin=False
        is_client=False
        is_sub_client=False
        is_case_manager=False
        
        if current_profile_type == ProfileType.admin.name:
            is_admin=True
        elif current_profile_type == ProfileType.client.name:
            is_client=True
        elif current_profile_type == ProfileType.sub_client.name:
            is_sub_client=True
        elif current_profile_type == ProfileType.case_manager.name:
            is_case_manager=True

        # Only admin, client, or case_manager can assign professionals
        if not (is_admin or is_client or is_sub_client or is_case_manager):
            return Response({'error': 'You do not have permission to assign professionals'}, status=status.HTTP_403_FORBIDDEN)

        # Additional check: case managers can only assign arbitrators, not other case managers
        if is_case_manager and professional_type == 'case_manager':
            return Response({'error': 'Case managers can only assign arbitrators, not other case managers'}, 
                           status=status.HTTP_403_FORBIDDEN)

        # Additional check: clients can only assign to their own campaigns
        if is_client and campaign_id:
            client_profile = Profile.objects.get(user=user, profile_type=ProfileType.client.name)
            campaign = Campaign.objects.filter(id=campaign_id).first()
            if campaign and campaign.client.id != client_profile.id:
                return Response({'error': 'You can only assign professionals to your own campaigns'}, 
                               status=status.HTTP_403_FORBIDDEN)

        if not professional_type:
            return Response({'error': 'professional_type is required'}, status=400)

        if not assignments or not isinstance(assignments, dict):
            return Response({'error': 'assignments must be a dictionary of professional_id to case_ids'}, status=400)

        if campaign_id:
            case_manager_prefetch = Prefetch('case_manager_rv', queryset=Profile.objects.select_related('user'), to_attr='prefetched_case_managers')

            # Fetch disputes and prefetch related case managers
            disputes = Dispute.objects.filter(campaign_id=campaign_id).order_by('id') \
                .prefetch_related(case_manager_prefetch)

            # Additional RBAC: case managers can only assign to disputes they're assigned to
            if is_case_manager:                
                disputes = disputes.filter(case_manager_rv=current_profile)
                if not disputes.exists():
                    return Response({'error': 'You are not assigned to any disputes in this campaign'}, 
                                   status=status.HTTP_403_FORBIDDEN)

            total_disputes = disputes.count()

            total_assigned_cases = sum(assignments.values())

            if total_disputes != total_assigned_cases:
                campaign = Campaign.objects.filter(id=campaign_id).first()
                return Response(
                    {'error': f'Total number of cases assigned ({total_assigned_cases}) does not match the total number of disputes ({total_disputes}) associated with this campaign id {campaign.id}.'},
                    status=400
                )

            disputes_list = list(disputes)
            dispute_index = 0

            disputes_to_update = []
            m2m_relations = []
            emails_to_send = []  # To hold email data for batch sending

            with transaction.atomic():
                if professional_type == 'case_manager':
                    Dispute.case_manager_rv.through.objects.filter(dispute_id__in=[d.id for d in disputes_list]).delete()
                elif professional_type == 'arbitrator':
                    Dispute.arbitrator_rv.through.objects.filter(dispute_id__in=[d.id for d in disputes_list]).delete()
                else:
                    return Response({'error': 'Invalid professional_type provided.'}, status=400)

                for prof_id, case_count in assignments.items():
                    try:
                        professional = Profile.objects.get(user__id=prof_id)
                    except Profile.DoesNotExist:
                        return Response({'error': f'Professional with id {prof_id} does not exist'}, status=400)
                    professionals_email = professional.user.email
                    professionals_name = professional.user.first_name + professional.user.last_name
                    professionals_email
                    professionals_email
                    for _ in range(case_count):
                        if dispute_index >= len(disputes_list):
                            break

                        dispute = disputes_list[dispute_index]
                        dispute_index += 1

                        if professional_type == 'case_manager':
                            dispute.case_manager_assigned_date = timezone.now()
                            m2m_relations.append(dispute.case_manager_rv.through(
                                dispute_id=dispute.id,
                                profile_id=professional.id
                            ))
                        elif professional_type == 'arbitrator':
                            # case_manager = dispute.prefetched_case_managers[0] if dispute.prefetched_case_managers else None
                            # emails_to_send.append((send_arbitrator_assigned, [professionals_email, professionals_name, dispute.name, dispute.id, case_manager.user.first_name + case_manager.user.last_name, case_manager.phone_number, case_manager.user.email]))
                            m2m_relations.append(dispute.arbitrator_rv.through(
                                dispute_id=dispute.id,
                                profile_id=professional.id
                            ))
                            # run_time = datetime.datetime.now() + timedelta(hours=1)
                            # scheduler.add_job(
                            #     send_delayed_email_arbirtrator_assigned,
                            #     'date',
                            #     run_date=run_time,
                            #     args=[professional.user.email, professional.user.first_name + professional.user.last_name],
                            #     misfire_grace_time=600
                            # )
                            # emails_to_send.append((sendBorrowerNotifyArbitratorAssigned, [case_manager.user.email, case_manager.user.first_name, dispute.name, dispute.id, case_manager.user.email]))

                            dispute.arbitrator_assigned_date = timezone.now()
                            dispute.arbitrator_status = "agree"

                        disputes_to_update.append(dispute)                      


                # Bulk Update and Bulk Create
                Dispute.objects.bulk_update(
                    disputes_to_update,
                    ['case_manager_assigned_date', 'arbitrator_assigned_date', 'arbitrator_status']
                )
                if m2m_relations:
                    if professional_type == 'case_manager':
                        Dispute.case_manager_rv.through.objects.bulk_create(m2m_relations)
                    elif professional_type == 'arbitrator':
                        Dispute.arbitrator_rv.through.objects.bulk_create(m2m_relations)

                # Update campaign table
                campaign = Campaign.objects.get(id=campaign_id)
                if professional_type == 'case_manager':
                    campaign.case_managers_assigned = True
                    campaign.assigned_case_managers_at = timezone.now()
                elif professional_type == 'arbitrator':
                    campaign.arbitrators_assigned = True
                    campaign.assigned_arbitrators_at = timezone.now()
                campaign.save()

                # Execute the emails in a batch asynchronously
                # executor.submit(send_emails_in_batch, emails_to_send)

            return Response({'success': 'Cases successfully assigned.'}, status=200)
        else:
            return Response({'error': 'campaign_id is required'}, status=400)


class ClientExcelView(APIView):
    permission_classes = [IsAuthenticated, IsAdminOrClient]

    def get(self, request):
        # Check if user is client
        user = request.user
        current_profile = Profile.objects.get(user=user)
        current_profile_type = current_profile.profile_type

        campaigns = []
        if current_profile_type == ProfileType.client.name:
            # If user is client, use their profile
            campaigns = Campaign.objects.filter(client=current_profile)
        else:
            # For admin users, require client_id
            client = request.query_params.get('client_id')
            if not client:
                return Response({"error":"client id is required"})            
            
            client_profile = get_object_or_404(Profile,user__id=client)
                        
            if current_profile_type == ProfileType.sub_client.name:
                client = Profile.objects.get(id=current_profile.parent_client.id)
                if client == client_profile:
                    campaigns = Campaign.objects.filter(client=client)
                else:
                    return Response({'error':'Not authorized to view data'},status=status.HTTP_403_FORBIDDEN)
            elif current_profile_type == ProfileType.case_manager.name:
                campaign_ids = Dispute.objects.filter(case_manager_rv=current_profile,client=client_profile).values_list('campaign',flat=True).distinct()
                campaigns = Campaign.objects.filter(id__in = campaign_ids)
            elif current_profile_type == ProfileType.arbitrator.name:
                return Response({'error':'Not authorized to perform this action'},status=status.HTTP_403_FORBIDDEN)
            elif current_profile_type == ProfileType.admin.name:
                campaigns = Campaign.objects.filter(client=client_profile)
                

        data = []

        for campaign in campaigns:
            created_by_name = ""
            if campaign.created_by:
                created_by_name = f"{campaign.created_by.first_name} {campaign.created_by.last_name}".strip()

            data.append({
                "excel_name": campaign.excel_file_name,
                "excel_display_name": campaign.excel_file_name.split('/')[-1],
                "created_at": campaign.created_at,
                "total_rows": campaign.total_rows,
                "created_by": created_by_name,
                "id": campaign.id
            })

        return Response(data)

class DashboardSheetsView(APIView):
    permission_classes = [IsAuthenticated, IsAdminOrClient]

    def get(self, request):
        # Check if user is client
        user = request.user
        is_admin = Profile.objects.filter(user=user, profile_type=ProfileType.admin.name).exists()
        is_client = Profile.objects.filter(user=user, profile_type=ProfileType.client.name).exists()

        campaigns = Campaign.objects.all()

        # If user is client, only show their campaigns
        if is_client and not is_admin:
            client_profile = Profile.objects.get(user=user, profile_type=ProfileType.client.name)
            campaigns = campaigns.filter(client=client_profile)
        else:
            # For admin users, filter by client_id if provided
            client_id = request.query_params.get('client_id')
            if client_id:
                campaigns = campaigns.filter(client_id=client_id)

        campaign_data = []
        for campaign in campaigns:
            campaign_serialized = CampaignSerializer(campaign).data
            notices = Notice.objects.filter(dispute__campaign=campaign)
            if client_id:
                notices = notices.filter(dispute__client_id=client_id)
            notice_serializer = NoticeSerializer(notices, many=True)
            campaign_serialized['notices'] = notice_serializer.data
            campaign_data.append(campaign_serialized)

        return Response(campaign_data)


class ExcelDownloadById(APIView):
    permission_classes = [IsAuthenticated, IsAdminOrClient]

    def get(self, request, pk):
        campaign = get_object_or_404(Campaign, pk=pk)

        if not campaign.client:
            return Response({'error':'No client associated with this campaign'},status=status.HTTP_400_BAD_REQUEST)

        # Check if user is client
        user = request.user
        current_profile = get_object_or_404(Profile,user=user)
        if not validate_profile_campaign(current_profile,campaign):
            return Response({'error': 'You do not have permission to access this campaign'},
                               status=status.HTTP_403_FORBIDDEN)          

        if not campaign.excel_file_name:
            return Response({"error": "PDF file not available"}, status=404)

        signed_url = gcs_manager.generate_signed_url(campaign.excel_file_name)

        return Response({"signed_url": signed_url})

class DisputeFileUploadView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, dispute_id):
        user = request.user
        current_profile = get_object_or_404(Profile,user=user)        

        try:
            dispute = Dispute.objects.get(id=dispute_id)
        except Dispute.DoesNotExist:
            return Response({"error": "Dispute not found."}, status=status.HTTP_404_NOT_FOUND)

        if not validate_profile_dispute(current_profile,dispute):
            return Response({'error': 'You do not have permission to access this dispute'},
                               status=status.HTTP_403_FORBIDDEN)  

       
        # Get files directly related to this dispute from database
        files = File.objects.filter(dispute=dispute)

        # Get campaign_id and loan_id from the dispute
        campaign_id = dispute.campaign.id if dispute.campaign else None
        loan_id = dispute.loan_id

        # Check additional GCS paths for related files
        additional_files = []

        if campaign_id and loan_id:  # Only proceed if both campaign_id and loan_id exist
            try:
                # Check different possible paths in GCS
                possible_paths = [
                    f"notices/{campaign_id}/files/{loan_id}",
                ]

                # Get all files to check for folders
                all_files = File.objects.filter(path__startswith=f"notices/{campaign_id}/files/")

                # Extract unique folder names from file paths
                folder_names = set()
                for file in all_files:
                    path_parts = file.path.split('/')
                    if len(path_parts) >= 5:  # File in folder: notices/{campaign_id}/files/{folder_name}/{filename}
                        folder_names.add(path_parts[3])

                # Add folder-based paths
                for folder_name in folder_names:
                    possible_paths.append(f"notices/{campaign_id}/files/{folder_name}/{loan_id}")

                # Also check the root path
                possible_paths.append(f"notices/{campaign_id}/files/{loan_id}")

                # List files in each path
                for prefix in possible_paths:
                    try:
                        gcs_files = gcs_manager.list_files(prefix=prefix)
                        for gcs_file_path in gcs_files:
                            # Skip directory entries
                            if gcs_file_path.endswith('/'):
                                continue

                            # Determine folder name from path
                            path_parts = gcs_file_path.split('/')
                            folder_name = path_parts[3] if len(path_parts) >= 5 else 'root'

                            # Extract filename from the full path
                            filename = path_parts[-1]

                            # Try to get file details from database if it exists
                            try:
                                db_file = File.objects.get(path=gcs_file_path)
                                additional_files.append({
                                    "id": db_file.id,
                                    "name": db_file.name,
                                    "path": gcs_file_path,
                                    "type": db_file.type,
                                    "size": db_file.size,
                                    "folder_name": folder_name,
                                    "uploaded_by": db_file.uploaded_by.first_name + ' ' + db_file.uploaded_by.last_name if db_file.uploaded_by else None,
                                    "created": db_file.created,
                                    "modified": db_file.modified,
                                })
                            except File.DoesNotExist:
                                # If file doesn't exist in database, create entry with GCS info only
                                additional_files.append({
                                    "id": None,  # No database ID
                                    "name": filename,
                                    "path": gcs_file_path,
                                    "type": None,  # Cannot determine without database entry
                                    "size": None,  # Would need to get from GCS metadata
                                    "folder_name": folder_name,
                                    "uploaded_by": None,
                                    "created": None,
                                    "modified": None,
                                })
                    except Exception as path_error:
                        print(f"Error fetching files from path {prefix}: {str(path_error)}")
                        continue

            except Exception as e:
                # Log error but continue with database files
                print(f"Error fetching GCS files: {str(e)}")

        # Add database files (dispute-specific files)
        database_files = [
            {
                "id": file.id,
                "name": file.name,
                "path": file.path,
                "type": file.type,
                "size": file.size,
                "folder_name": "dispute_files",  # Indicate these are dispute-specific files
                "uploaded_by": file.uploaded_by.first_name + ' ' + file.uploaded_by.last_name if file.uploaded_by else None,
                "created": file.created,
                "modified": file.modified,
            }
            for file in files
        ]

        # Combine database files with GCS files
        data = database_files + additional_files
        return Response(data, status=status.HTTP_200_OK)


    def post(self, request, dispute_id):
        uploaded_file = request.FILES.get('file')
        user = request.user
        profile = get_object_or_404(Profile,user=user)

        try:
            dispute = Dispute.objects.get(id=dispute_id)
        except Dispute.DoesNotExist:
            return Response({"error": "Dispute not found."}, status=status.HTTP_404_NOT_FOUND)

        if not validate_profile_dispute(profile,dispute):
            return Response({'error': 'You do not have permission to access this dispute'},
                               status=status.HTTP_403_FORBIDDEN)  

        if not uploaded_file:
            return Response({"error": "No file provided."}, status=status.HTTP_400_BAD_REQUEST)
        

        # Upload to GCS
        unique_filename = get_timestamped_filename(uploaded_file.name)
        file_path = f"notices/{dispute.campaign.id}/dispute_files/{dispute_id}/{unique_filename}"
        gcs_manager.upload_file(file_path, uploaded_file)
        
        file_type = mimetypes.guess_type(uploaded_file.name)[0] or "application/octet-stream"
        file_size = uploaded_file.size

        File.objects.create(
            name=uploaded_file.name,
            uploaded_by=request.user,
            path=file_path,
            type=file_type,
            size=str(file_size),
            dispute=dispute
        )

        return Response({"message": "File uploaded successfully"}, status=status.HTTP_201_CREATED)
    

class DisputeFileDownloadById(APIView):
    permission_classes = [IsAuthenticated]  

    def get(self, request, file_id):
        file = get_object_or_404(File, id=file_id)
        
        if not file.path:
            return Response({"error": "File path is not available."}, status=status.HTTP_400_BAD_REQUEST)
        
        user=request.user
        current_profile = get_object_or_404(Profile,user=user)
        dispute = file.dispute

        if not validate_profile_dispute(current_profile,dispute):
            return Response({'error': 'You do not have permission to access this dispute'},
                               status=status.HTTP_403_FORBIDDEN)  

        try:
            signed_url = gcs_manager.generate_signed_url(file.path)
        except Exception as e:
            return Response({"error": "Failed to generate signed URL.", "details": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        return Response({"signed_url": signed_url}, status=status.HTTP_200_OK)


class BulkFileUploadView(APIView):
    permission_classes = [IsAuthenticated,IsAdminOrClient]

    def upload_notice(self,uploaded_file,file_path,campaign_id,template_id,user):
        """Upload a single notice to storage. This is differerent from bulk uploaded files for campaign"""
        dispute=None
        try:
            dispute = Dispute.objects.get(campaign_id=campaign_id,loan_id=uploaded_file.name.rstrip(".pdf"))
        except Dispute.DoesNotExist:
            return None, {
                "filename":uploaded_file.name,
                "campaign_id":campaign_id,
                "message": "No loan_id found in the campaign for the filename provided."
            }       

        try:              
            gcs_manager.upload_file(file_path, uploaded_file)            
            
            data = {
                'file_path': file_path,
                'file_name': uploaded_file.name,
                'notice_type': 'Default',
                'created_by': user,
                'created_at': datetime.now(),
                'dispute_id': dispute.id,
                'template_id': template_id
            }
            notice = Notice.objects.create(**data)
            
            return {
                "id":notice.id,
                "file_name":notice.file_name,
                "file_path":notice.file_path,
                "dispute_id":notice.dispute.id,
                "template_id":notice.template.id,
                "message": "Notice uploaded successfully to cloud storage."
            },None
        except Exception as e:
            return None, {
                "name": uploaded_file.name,    
                'file_path': file_path,
                "dispute_id":dispute.id,           
                "error": str(e)
            }

    def upload_single_file(self, uploaded_file, file_path, folder_name, campaign_id, user):
        """
        Upload a single file to a specific folder with error handling
        Returns tuple: (success_data, error_data)
        """
        try:
              
            gcs_manager.upload_file(file_path, uploaded_file)
            
            file_type = mimetypes.guess_type(uploaded_file.name)[0] or "application/octet-stream"
            file_size = uploaded_file.size

            file_obj = File.objects.create(
                name=uploaded_file.name,
                uploaded_by=user,
                path=file_path,
                type=file_type,
                size=str(file_size)
            )

            return {
                "id": file_obj.id,
                "name": file_obj.name,
                "path": file_obj.path,
                "type": file_obj.type,
                "size": file_obj.size,
                "folder_name": folder_name or "root",  # Show "root" if no folder_name
                "campaign_id": campaign_id,
                "message": "File uploaded successfully to folder" if folder_name else "File uploaded successfully to root"
            }, None

        except Exception as e:
            return None, {
                "name": uploaded_file.name,
                "folder_name": folder_name or "root",
                "error": str(e)
            }

    def get(self, request, campaign_id):
        user = request.user
        profile = get_object_or_404(Profile,user=user)
        
        if not campaign_id:
            return Response({'error':'Campaign_id not provided'},status=status.HTTP_400_BAD_REQUEST)
        try:
            campaign = Campaign.objects.get(id=campaign_id)
        except Campaign.DoesNotExist:
            return Response({"error": "Campaign not found."}, status=status.HTTP_404_NOT_FOUND)

        if not validate_profile_campaign(profile,campaign):
            return Response({"error": "You are not allowed to perform this action."}, status=status.HTTP_403_FORBIDDEN)

        # Get folder_name from query parameters to browse specific folder
        folder_name = request.query_params.get('folder_name')

        if folder_name:
            # Get files from specific folder
            files = File.objects.filter(path__startswith=f"notices/{campaign_id}/files/{folder_name}/")
            current_location = folder_name
        else:
            # Get all files to separate root files and files in folders
            all_files = File.objects.filter(path__startswith=f"notices/{campaign_id}/files/")

            # Separate root files and files in folders
            root_files = []
            folder_files = {}

            for file in all_files:
                path_parts = file.path.split('/')
                # Expected path: notices/{campaign_id}/files/{filename} or notices/{campaign_id}/files/{folder_name}/{filename}
                if len(path_parts) == 4:  # Root file: notices/{campaign_id}/files/{filename}
                    root_files.append(file)
                elif len(path_parts) >= 5:  # File in folder: notices/{campaign_id}/files/{folder_name}/{filename}
                    folder_name_from_path = path_parts[3]
                    if folder_name_from_path not in folder_files:
                        folder_files[folder_name_from_path] = []
                    folder_files[folder_name_from_path].append(file)

            files = root_files
            current_location = "root"

        paginator = PageNumberPagination()
        page_size = request.query_params.get('page_size', 25)
        paginator.page_size = int(page_size)
        result_page = paginator.paginate_queryset(files, request)

        def generate_signed_url_for_file(file):
            try:
                signed_url = gcs_manager.generate_signed_url(file.path)
                path_parts = file.path.split('/')

                if len(path_parts) >= 5:
                    folder_name = path_parts[3]
                else:
                    folder_name = 'root'

                return {
                    "id": file.id,
                    "name": file.name,
                    "path": file.path,
                    "type": file.type,
                    "size": file.size,
                    "folder_name": folder_name,
                    "signed_url": signed_url,
                    "uploaded_by": file.uploaded_by.first_name + ' ' + file.uploaded_by.last_name if file.uploaded_by else None,
                    "created": file.created,
                    "modified": file.modified,
                }
            except Exception as e:
                path_parts = file.path.split('/')
                
                if len(path_parts) >= 5:
                    folder_name = path_parts[3]
                else:
                    folder_name = 'root'
                    
                return {
                    "id": file.id,
                    "name": file.name,
                    "path": file.path,
                    "type": file.type,
                    "size": file.size,
                    "folder_name": folder_name,
                    "signed_url": None,
                    "signed_url_error": str(e),
                    "uploaded_by": file.uploaded_by.first_name + ' ' + file.uploaded_by.last_name if file.uploaded_by else None,
                    "created": file.created,
                    "modified": file.modified,
                }

        # Use threading for signed URL generation
        files_data = []
        with ThreadPoolExecutor(max_workers=10) as executor:
            future_to_file = {executor.submit(generate_signed_url_for_file, file): file for file in result_page}
            for future in as_completed(future_to_file):
                files_data.append(future.result())

        # Prepare response data
        response_data = {
            "files": files_data,
            "current_location": current_location,
        }

        # If we're in root, also provide folders information
        if not request.query_params.get('folder_name'):
            folders_info = []
            for folder_name, folder_files_list in folder_files.items():
                folders_info.append({
                    "folder_name": folder_name,
                    "file_count": len(folder_files_list),
                    "total_size": sum(int(f.size) for f in folder_files_list if f.size.isdigit()),
                    "last_modified": max(f.modified for f in folder_files_list) if folder_files_list else None
                })
            response_data["folders"] = folders_info
            response_data["total_root_files"] = len(files)
            response_data["total_folders"] = len(folders_info)

        # Create custom paginated response
        if hasattr(paginator, 'get_paginated_response'):
            paginated_response = paginator.get_paginated_response(files_data)
            # Update the paginated response data with our custom structure
            paginated_response.data.update(response_data)
            return paginated_response
        else:
            return Response(response_data)

    def post(self, request, campaign_id):
        uploaded_files = request.FILES.getlist('files')
        user = request.user
        folder_name = request.data.get('folder_name')
        
        profile = get_object_or_404(Profile,user=user)
        if request.data.get('flow').strip().lower() not in ['notice','casefiles']:
            return Response({"error": "Wrong flow type."}, status=status.HTTP_404_NOT_FOUND)
        is_notice = True if request.data.get('flow').strip().lower() == 'notice' else False

        if is_notice:
            template_id = request.data.get('template_id')            
            if not template_id:
                return Response({"error": "Template Id is missing"},status=status.HTTP_404_NOT_FOUND)
            try:
                template = Template.objects.get(id=template_id)
            except Template.DoesNotExist:
                return Response({"error": "Template not found for Id"},status=status.HTTP_404_NOT_FOUND)
            template_name = template.name
            cloud_storage_prefix = f"notices/{campaign_id}/{template_id}/{template_name}/"
        else:
            cloud_storage_prefix=f"notices/{campaign_id}/files/"

        # Clean folder_name - treat empty string as None
        if folder_name is not None:
            folder_name = folder_name.strip()
            if not folder_name:  # Empty string after stripping
                folder_name = None            

        try:
            campaign = Campaign.objects.get(id=campaign_id)
        except Campaign.DoesNotExist:
            return Response({"error": "Campaign not found."}, status=status.HTTP_404_NOT_FOUND)

        if not validate_profile_campaign(profile,campaign):
            return Response({"error": "You are not allowed to perform this action."}, status=status.HTTP_403_FORBIDDEN)

        # Check for unique folder name condition 
        #This is only for upload of case files. Bulk notice upload will not have data in File and Folder tables.
        if folder_name and not is_notice:
            # Check if folder already exists in this campaign
            existing_folder_files = File.objects.filter(
                path__startswith=f'{cloud_storage_prefix}{folder_name}/'
            ).exists()

            if existing_folder_files:
                return Response({
                    "error": f"Folder '{folder_name}' already exists in this campaign. Please choose a different folder name."
                }, status=status.HTTP_400_BAD_REQUEST)

        if not uploaded_files:
            return Response({"error": "No files provided."}, status=status.HTTP_400_BAD_REQUEST)

        # Check file limit - maximum 1000 files allowed
        if len(uploaded_files) > 1000:
            return Response({
                "error": f"Too many files. Maximum 1000 files allowed, but {len(uploaded_files)} were provided."
            }, status=status.HTTP_400_BAD_REQUEST)

        total_files = len(uploaded_files)
        max_workers = 10 if total_files <= 100 else 20 if total_files <= 500 else 35

        uploaded_file_data = []
        failed_uploads = []

        success_lock = threading.Lock()
        failure_lock = threading.Lock()

        start_time = time.time()

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_file = {}
            
            for uploaded_file in uploaded_files:
                # Construct file path based on whether folder_name is provided
                if is_notice:                    
                    file_path = f"{cloud_storage_prefix}{uploaded_file.name}"
                    
                    future = executor.submit(
                    self.upload_notice,
                    uploaded_file,
                    file_path,                    
                    campaign_id,
                    template_id,
                    user     
                )
                else:
                    if folder_name:                    
                        file_path = f"{cloud_storage_prefix}{folder_name}/{uploaded_file.name}"
                    else:
                        file_path = f"{cloud_storage_prefix}{uploaded_file.name}"
                
                    future = executor.submit(
                        self.upload_single_file,
                        uploaded_file,
                        file_path,
                        folder_name,
                        campaign_id,
                        user
                    )
                future_to_file[future] = uploaded_file

            for future in as_completed(future_to_file):
                uploaded_file = future_to_file[future]
                try:
                    success_data, error_data = future.result()

                    if success_data:
                        with success_lock:
                            uploaded_file_data.append(success_data)

                    if error_data:
                        with failure_lock:
                            failed_uploads.append(error_data)

                except Exception as e:
                    with failure_lock:
                        failed_uploads.append({
                            "name": uploaded_file.name,
                            "folder_name": folder_name or "root",
                            "error": f"Thread execution error: {str(e)}"
                        })

        end_time = time.time()
        upload_duration = end_time - start_time
        files_per_second = len(uploaded_file_data) / upload_duration if upload_duration > 0 else 0

        if is_notice:
            template.renaming_and_splitting_done = True
            template.save()

        response_data = {
            "successful_uploads": uploaded_file_data,
            "failed_uploads": failed_uploads,
            "total_files": len(uploaded_files),
            "successful_count": len(uploaded_file_data),
            "failed_count": len(failed_uploads),
            "upload_duration_seconds": round(upload_duration, 2),
            "files_per_second": round(files_per_second, 2),
            "threads_used": max_workers,
            "upload_location": folder_name if folder_name else "root"
        }

        return Response(response_data, status=status.HTTP_207_MULTI_STATUS if failed_uploads else status.HTTP_201_CREATED)


class GetProfessionalsByClientView(APIView):
    permission_classes = [IsAuthenticated,IsAdminOrClient]

    def get(self, request):
        client_id = request.query_params.get('client_id')
        campaign_id = request.query_params.get('campaign_id')
        professional_type = request.query_params.get('type')  # 'arbitrator' or 'case_manager'

        # Validate that at least one ID is provided
        if not client_id and not campaign_id:
            return Response({'error': 'Either client_id or campaign_id is required'}, status=status.HTTP_400_BAD_REQUEST)

        # Validate that both IDs are not provided simultaneously
        if client_id and campaign_id:
            return Response({'error': 'Provide either client_id or campaign_id, not both'}, status=status.HTTP_400_BAD_REQUEST)

        # Check user permissions
        user = request.user
        current_profile = get_object_or_404(Profile,user=user)
        current_profile_type = current_profile.profile_type       

        try:
            client_disputes = None

            if client_id:
                # Handle client_id logic (existing functionality)

                # Verify the requesting user has permission to view this client's data
                if current_profile_type == ProfileType.admin.name:
                    pass
                elif current_profile_type == ProfileType.client.name:                    
                    if str(current_profile.user.id) != client_id:
                        return Response({'error': 'You can only view professionals for your own account'}, 
                                       status=status.HTTP_403_FORBIDDEN)
                elif current_profile_type == ProfileType.sub_client.name:
                    if str(current_profile.parent_client.user.id) != client_id:
                        return Response({'error': 'You can only view professionals for your own account'}, 
                                       status=status.HTTP_403_FORBIDDEN)
                elif current_profile_type == ProfileType.case_manager.name:
                    clients = Dispute.objects.filter(case_manager_rv=current_profile).values_list('client__id')
                    if client_id not in clients:
                        return Response({'error': 'You can only view professionals for your own account'}, 
                                       status=status.HTTP_403_FORBIDDEN)
                elif current_profile_type == ProfileType.arbitrator.name:
                    clients = Dispute.objects.filter(arbitrator_rv=current_profile).values_list('client__id')
                    if client_id not in clients:
                        return Response({'error': 'You can only view professionals for your own account'}, 
                                       status=status.HTTP_403_FORBIDDEN)

                # Get the client profile
                client_profile = Profile.objects.get(user_id=client_id, profile_type=ProfileType.client.name)

                # Get all disputes for this client
                client_disputes = Dispute.objects.filter(client=client_profile)

            elif campaign_id:
                # Get the campaign
                try:
                    from .models import Campaign  # Adjust import path as needed
                    campaign = Campaign.objects.get(id=campaign_id)
                except Campaign.DoesNotExist:
                    return Response({'error': 'Campaign not found'}, status=status.HTTP_404_NOT_FOUND)

                if not validate_profile_campaign(current_profile,campaign):
                        return Response({'error': 'You can only view professionals for your own campaigns'}, 
                                       status=status.HTTP_403_FORBIDDEN)

                # Get all disputes for this campaign
                client_disputes = Dispute.objects.filter(campaign=campaign)

            result = {}

            # Get case managers if requested or if no specific type is requested
            if not professional_type or professional_type == 'case_manager':
                case_manager_ids = client_disputes.values_list('case_manager_rv__id', flat=True).distinct()
                case_managers = Profile.objects.filter(id__in=case_manager_ids, profile_type=ProfileType.case_manager.name)

                case_manager_data = []
                for cm in case_managers:
                    if cm:
                        case_manager_data.append({
                            'id': cm.id,
                            'name': f"{cm.user.first_name} {cm.user.last_name}".strip()
                        })
                result['case_managers'] = case_manager_data

            # Get arbitrators if requested or if no specific type is requested
            if not professional_type or professional_type == 'arbitrator':
                arbitrator_ids = client_disputes.values_list('arbitrator_rv__id', flat=True).distinct()
                arbitrators = Profile.objects.filter(id__in=arbitrator_ids, profile_type=ProfileType.arbitrator.name)

                arbitrator_data = []
                for arb in arbitrators:
                    if arb:
                        arbitrator_data.append({
                            'id': arb.user_id,
                            'name': f"{arb.user.first_name} {arb.user.last_name}".strip()
                        })
                result['arbitrators'] = arbitrator_data

            return Response(result, status=status.HTTP_200_OK)

        except Profile.DoesNotExist:
            return Response({'error': 'Client profile not found'}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class DisputeFilesZipExportView(APIView):
    permission_classes = [IsAuthenticated, IsAdminOrClient]

    def post(self, request):
        """Main endpoint to export dispute files as ZIP"""
        try:
            # Get dispute IDs from request
            dispute_ids = request.data.get('dispute_ids', [])

            # Check if Excel report should be included (default: True)
            include_excel = request.data.get('include_excel', True)

            # Export only Excel file (skip all dispute files)
            excel_only = request.query_params.get('excel', False)

            if not dispute_ids or not isinstance(dispute_ids, list):
                return Response(
                    {"error": "dispute_ids is required and must be a list"}, 
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Validate user permissions and filter disputes
            disputes = self.get_accessible_disputes(request.user, dispute_ids)

            if not disputes.exists():
                return Response(
                    {"error": "No accessible disputes found for the provided IDs"}, 
                    status=status.HTTP_404_NOT_FOUND
                )

            # Generate ZIP file with high performance
            zip_response = self.generate_zip_export(disputes, include_excel, excel_only)
            return zip_response

        except Exception as e:
            return Response(
                {"error": f"ZIP export failed: {str(e)}"}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def get_accessible_disputes(self, user, dispute_ids):
        """Filter disputes based on user permissions"""
        try:
            current_user_profile = Profile.objects.get(user=user)
            current_user_profile_type = current_user_profile.profile_type
            
            # Base queryset with dispute IDs - optimized with select_related
            disputes = Dispute.objects.filter(id__in=dispute_ids).select_related(
                'client__user',
                'campaign',
                'claimant__user'
            ).prefetch_related(
                'arbitrator_rv__user'
            )
            
            # Apply user-specific filters
            if current_user_profile_type == ProfileType.admin.name:
                pass
            elif current_user_profile_type == ProfileType.client.name:
                disputes = disputes.filter(client=current_user_profile)
            elif current_user_profile_type == ProfileType.sub_client.name:
                disputes = disputes.filter(client=current_user_profile.parent_client)
            elif current_user_profile_type == ProfileType.case_manager.name:
                disputes = disputes.filter(case_manager_rv=current_user_profile)
            elif current_user_profile_type == ProfileType.arbitrator.name:
                disputes = disputes.filter(arbitrator_rv=current_user_profile)
            else:
                disputes = disputes.none()
            
            return disputes
            
        except Profile.DoesNotExist:
            return Dispute.objects.none()

    def generate_zip_export(self, disputes, include_excel=True, excel_only=False):
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        # If excel_only is True, return Excel file directly (no ZIP)
        if excel_only:
            excel_start = time.time()
            excel_response = self.generate_excel_response(disputes, timestamp)
            excel_time = time.time() - excel_start
            # print(f"Excel file generated in {excel_time:.2f} seconds")
            return excel_response

        # Regular ZIP export logic
        zip_buffer = BytesIO()

        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED, compresslevel=1) as zip_file:
            # Add Excel report if requested (default: True)
            if include_excel:
                excel_start = time.time()
                self.add_excel_report_to_zip(zip_file, disputes)
                excel_time = time.time() - excel_start
                # print(f"Excel report generated in {excel_time:.2f} seconds")
            else:
                print("Excel report skipped")
            
            # Process disputes with DIRECT GCS file access
            self.add_dispute_files_from_gcs(zip_file, disputes)

        zip_buffer.seek(0)

        # Create HTTP response for ZIP
        response = HttpResponse(
            zip_buffer.getvalue(),
            content_type='application/zip'
        )
        filename = f'disputes_export_{timestamp}.zip'
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        response['Content-Length'] = len(zip_buffer.getvalue())

        end_time = time.time()
        # print(f"ZIP generation completed in {end_time - start_time:.2f} seconds")

        return response

    def get_actual_folders_in_files_directory(self, campaign_id):
        """
        Get actual folder names that exist in notices/{campaign_id}/files/
        Using the same logic as DisputeFileUploadView
        """
        try:
            # Get all files to check for folders (same as DisputeFileUploadView)
            all_files = File.objects.filter(path__startswith=f"notices/{campaign_id}/files/")

            # Extract unique folder names from file paths
            folder_names = set()
            for file in all_files:
                path_parts = file.path.split('/')
                if len(path_parts) >= 5:  # File in folder: notices/{campaign_id}/files/{folder_name}/{filename}
                    folder_names.add(path_parts[3])

            # print(f"Found actual folders in campaign {campaign_id}: {list(folder_names)}")
            return list(folder_names)
                
        except Exception as e:
            print(f"Error checking folders for campaign {campaign_id}: {e}")
            return []

    def get_files_from_gcs_directly(self, campaign_id, loan_id):
        """
        Get files directly from GCS using the same logic as DisputeFileUploadView
        Returns list of actual file paths that exist in GCS
        """
        found_files = []
        
        try:
            # Check different possible paths in GCS (same as DisputeFileUploadView)
            possible_paths = [
                f"notices/{campaign_id}/files/{loan_id}",
            ]

            # Get actual folder names from database
            folder_names = self.get_actual_folders_in_files_directory(campaign_id)

            # Add folder-based paths
            for folder_name in folder_names:
                possible_paths.append(f"notices/{campaign_id}/files/{folder_name}/{loan_id}")

            # Also check the root path
            possible_paths.append(f"notices/{campaign_id}/files/{loan_id}")

            # List files in each path (same as DisputeFileUploadView)
            for prefix in possible_paths:
                try:
                    gcs_files = gcs_manager.list_files(prefix=prefix)
                    for gcs_file_path in gcs_files:
                        # Skip directory entries
                        if gcs_file_path.endswith('/'):
                            continue
                        
                        # Only add PDF files
                        if gcs_file_path.lower().endswith('.pdf'):
                            found_files.append(gcs_file_path)
                            # print(f"Found GCS file: {gcs_file_path}")
                            
                except Exception as path_error:
                    print(f"Error fetching files from path {prefix}: {str(path_error)}")
                    continue

        except Exception as e:
            print(f"Error fetching GCS files for campaign {campaign_id}, loan {loan_id}: {str(e)}")

        return found_files

    def get_valid_dispute_file_paths(self, disputes):
        """
        Generate ALL possible dispute file paths using DisputeFileUploadView logic
        Find actual files that exist in GCS bucket
        """
        valid_paths = set()
        disputes_list = list(disputes)

        if not disputes_list:
            return valid_paths

        # Group by campaign for processing
        campaign_groups = {}
        for dispute in disputes_list:
            campaign_id = dispute.campaign.id if dispute.campaign else None
            if campaign_id and dispute.loan_id:
                if campaign_id not in campaign_groups:
                    campaign_groups[campaign_id] = {
                        'client_profile': dispute.client,
                        'disputes': []
                    }
                campaign_groups[campaign_id]['disputes'].append(dispute)

        # print(f"Processing {len(campaign_groups)} campaigns using DisputeFileUploadView logic")

        # Process each campaign
        for campaign_id, campaign_data in campaign_groups.items():
            client_profile = campaign_data['client_profile']
            campaign_disputes = campaign_data['disputes']

            # Get templates with actual usage evidence
            template_definitions = self.get_campaign_templates_with_usage(
                campaign_disputes, client_profile, campaign_id
            )

            # Generate template-based paths
            for dispute in campaign_disputes:
                loan_id = dispute.loan_id
                if not loan_id:
                    continue

                # Template-based PDFs - existing logic
                for template_def in template_definitions:
                    template_id = template_def['template_id']
                    template_name = template_def['template_name']

                    # Only include if Notice exists
                    has_notice = Notice.objects.filter(
                        dispute=dispute, 
                        template_id=template_id
                    ).exists()

                    if has_notice:
                        # Full template path
                        template_path = f'notices/{campaign_id}/{template_id}/{template_name}/{loan_id}.pdf'
                        valid_paths.add(template_path)

                        # Short template path
                        alt_template_path = f'notices/{campaign_id}/{template_id}/{loan_id}.pdf'
                        valid_paths.add(alt_template_path)

                # NEW: Get files directly from GCS using DisputeFileUploadView logic
                gcs_files = self.get_files_from_gcs_directly(campaign_id, loan_id)
                for gcs_file_path in gcs_files:
                    valid_paths.add(gcs_file_path)

        print(f"Generated {len(valid_paths)} total file paths using GCS discovery")
        return valid_paths

    def generate_signed_url_for_dispute_file(self, file_path):
        """Generate signed URL for PDF file using GCS manager"""
        try:
            return gcs_manager.generate_signed_url(file_path, expiration_seconds=3600*24*7)
        except Exception as e:
            print(f"Failed to generate signed URL for {file_path}: {e}")
            return None

    def download_file_from_gcs(self, file_path):
        """Download file directly from GCS using signed URL"""
        try:
            signed_url = self.generate_signed_url_for_dispute_file(file_path)
            if not signed_url:
                return None

            response = requests.get(
                signed_url,
                timeout=30,
                stream=False,
                headers={'Connection': 'close'}
            )

            if response.status_code == 200 and len(response.content) > 0:
                return response.content
            else:
                # print(f"Failed to download {file_path}: HTTP {response.status_code}")
                return None
                
        except Exception as e:
            print(f"Download failed for {file_path}: {e}")
            return None

    def add_dispute_files_from_gcs(self, zip_file, disputes):
        """
        Add ALL dispute files directly from GCS - never skip any found file
        Add every file with unique names, no duplicate checking
        """
        start_time = time.time()
        disputes_list = list(disputes)

        if not disputes_list:
            print("No disputes to process")
            return

        # Get ALL possible file paths
        valid_file_paths = self.get_valid_dispute_file_paths(disputes)

        if not valid_file_paths:
            print("No file paths generated")
            return

        # print(f"Attempting to download {len(valid_file_paths)} files from GCS")

        # Organize files by dispute for better ZIP structure
        dispute_file_map = self.organize_paths_by_dispute(valid_file_paths, disputes_list)

        total_files = 0
        total_attempts = 0
        file_counter = {}  # Track filename counts for unique naming

        for dispute in disputes_list:
            dispute_files = dispute_file_map.get(dispute.id, [])

            if dispute_files:
                dispute_folder = f"dispute_{dispute.id}/"
                
                # Process ALL files for this dispute - NEVER SKIP ANY
                for file_path in dispute_files:
                    total_attempts += 1
                    # print(f"Attempting to download: {file_path}")
                    
                    file_content = self.download_file_from_gcs(file_path)
                    
                    # If file found, ALWAYS ADD IT with unique name
                    if file_content:
                        # Create unique filename every time
                        original_name = os.path.basename(file_path)
                          # Extract template_id from the file path if possible
                        path_parts = file_path.split('/')
                        template_id = path_parts[2] if len(path_parts) > 2 and path_parts[2].isdigit() else "template"
                        base_name = f"{template_id}_{original_name}"
                        # Add counter to make each filename unique
                        if base_name in file_counter:
                            file_counter[base_name] += 1
                            unique_filename = f"{base_name.rsplit('.', 1)[0]}_{file_counter[base_name]}.{base_name.rsplit('.', 1)[1]}"
                        else:
                            file_counter[base_name] = 1
                            unique_filename = base_name
                        
                        zip_path = f"{dispute_folder}{unique_filename}"
                        
                        zip_file.writestr(zip_path, file_content)
                        total_files += 1
                        # print(f"✓ Added to ZIP: {zip_path}")
                    else:
                        # File not found - skip silently (no error files in ZIP)
                        # print(f"✗ File not found, skipping: {file_path}")
                        pass

                # processing_time = time.time() - start_time
                # success_rate = (total_files/total_attempts*100) if total_attempts > 0 else 0
                # # print(f"RESULT: Successfully added {total_files}/{total_attempts} files ({success_rate:.1f}% success) in {processing_time:.2f} seconds")

    def organize_paths_by_dispute(self, file_paths, disputes_list):
        """Organize file paths by dispute ID for processing"""
        dispute_file_map = {}
        
        for dispute in disputes_list:
            dispute_file_map[dispute.id] = []
            
            campaign_id = str(dispute.campaign.id) if dispute.campaign else None
            loan_id = str(dispute.loan_id) if dispute.loan_id else None
            
            if not (campaign_id and loan_id):
                continue
                
            for file_path in file_paths:
                if (campaign_id in file_path and loan_id in file_path):
                    dispute_file_map[dispute.id].append(file_path)

        return dispute_file_map

    def get_campaign_templates_with_usage(self, disputes, client_profile, campaign_id):
        """Get templates associated with campaign that have actual usage evidence"""
        if not campaign_id:
            return []

        # Get campaign templates
        campaign_template_ids = set(CampaignTemplate.objects.filter(
            campaign_id=campaign_id
        ).values_list('template_id', flat=True))

        if not campaign_template_ids:
            return []

        # Get client templates that match campaign templates
        client_templates = Template.objects.filter(
            profile=client_profile,
            id__in=campaign_template_ids,
            name__isnull=False
        ).exclude(name__exact='').select_related('email_template', 'whatsapp_template')

        # Build template definitions with usage evidence check
        template_definitions = []

        for template in client_templates:
            template_name = template.name.strip()

            # Check for actual usage evidence
            has_notice = Notice.objects.filter(
                dispute__in=disputes, template_id=template.id
            ).exists()

            has_entry = Entry.objects.filter(
                dispute__in=disputes, template_id=template.id
            ).exists()

            has_email = SendgridMail.objects.filter(
                dispute__in=disputes, template_id=template.id
            ).exists()

            # Only include templates with evidence
            if has_notice or has_entry or has_email:
                template_definitions.append({
                    'template_id': template.id,
                    'template_name': template_name,
                    'type': 'dispute_template'
                })

        # print(f"Found {len(template_definitions)} legitimate templates for campaign {campaign_id}")
        return template_definitions
        """
        Add ALL dispute files directly from GCS - never skip any found file
        Add every file with unique names, no duplicate checking
        """
        start_time = time.time()
        disputes_list = list(disputes)

        if not disputes_list:
            print("No disputes to process")
            return

        # Get ALL possible file paths
        valid_file_paths = self.get_valid_dispute_file_paths(disputes)

        if not valid_file_paths:
            print("No file paths generated")
            return

        # print(f"Attempting to download {len(valid_file_paths)} files from GCS")

        # Organize files by dispute for better ZIP structure
        dispute_file_map = self.organize_paths_by_dispute(valid_file_paths, disputes_list)
        
        total_files = 0
        total_attempts = 0
        file_counter = {}  # Track filename counts for unique naming

        for dispute in disputes_list:
            dispute_files = dispute_file_map.get(dispute.id, [])
            
            if dispute_files:
                dispute_folder = f"dispute_{dispute.id}/"
                
                # Process ALL files for this dispute - NEVER SKIP ANY
                for file_path in dispute_files:
                    total_attempts += 1
                    # print(f"Attempting to download: {file_path}")
                    
                    file_content = self.download_file_from_gcs(file_path)
                    
                    # If file found, ALWAYS ADD IT with unique name
                    if file_content:
                        # Create unique filename every time
                        original_name = os.path.basename(file_path)
                        base_name = f"dispute_{dispute.id}_{dispute.loan_id}_{original_name}"
                        
                        # Add counter to make each filename unique
                        if base_name in file_counter:
                            file_counter[base_name] += 1
                            unique_filename = f"{base_name.rsplit('.', 1)[0]}_{file_counter[base_name]}.{base_name.rsplit('.', 1)[1]}"
                        else:
                            file_counter[base_name] = 1
                            unique_filename = base_name
                        
                        zip_path = f"{dispute_folder}{unique_filename}"
                        
                        zip_file.writestr(zip_path, file_content)
                        total_files += 1
                        # print(f"✓ Added to ZIP: {zip_path}")
                    else:
                        # File not found - skip silently (no error files in ZIP)
                        # print(f"✗ File not found, skipping: {file_path}")
                        pass

        processing_time = time.time() - start_time
        success_rate = (total_files/total_attempts*100) if total_attempts > 0 else 0
        # print(f"RESULT: Successfully added {total_files}/{total_attempts} files ({success_rate:.1f}% success) in {processing_time:.2f} seconds")

    def organize_paths_by_dispute(self, file_paths, disputes_list):
        """Organize file paths by dispute ID for processing"""
        dispute_file_map = {}
        
        for dispute in disputes_list:
            dispute_file_map[dispute.id] = []
            
            campaign_id = str(dispute.campaign.id) if dispute.campaign else None
            loan_id = str(dispute.loan_id) if dispute.loan_id else None
            
            if not (campaign_id and loan_id):
                continue
                
            for file_path in file_paths:
                if (campaign_id in file_path and loan_id in file_path):
                    dispute_file_map[dispute.id].append(file_path)

        return dispute_file_map

    def get_campaign_templates_with_usage(self, disputes, client_profile, campaign_id):
        """Get templates associated with campaign that have actual usage evidence"""
        if not campaign_id:
            return []

        # Get campaign templates
        campaign_template_ids = set(CampaignTemplate.objects.filter(
            campaign_id=campaign_id
        ).values_list('template_id', flat=True))

        if not campaign_template_ids:
            return []

        # Get client templates that match campaign templates
        client_templates = Template.objects.filter(
            profile=client_profile,
            id__in=campaign_template_ids,
            name__isnull=False
        ).exclude(name__exact='').select_related('email_template', 'whatsapp_template')

        # Build template definitions with usage evidence check
        template_definitions = []

        for template in client_templates:
            template_name = template.name.strip()

            # Check for actual usage evidence
            has_notice = Notice.objects.filter(
                dispute__in=disputes, template_id=template.id
            ).exists()

            has_entry = Entry.objects.filter(
                dispute__in=disputes, template_id=template.id
            ).exists()

            has_email = SendgridMail.objects.filter(
                dispute__in=disputes, template_id=template.id
            ).exists()

            # Only include templates with evidence
            if has_notice or has_entry or has_email:
                template_definitions.append({
                    'template_id': template.id,
                    'template_name': template_name,
                    'type': 'dispute_template'
                })

        # print(f"Found {len(template_definitions)} legitimate templates for campaign {campaign_id}")
        return template_definitions

    # Keep existing methods for Excel generation
    def generate_excel_response(self, disputes, timestamp):
        """Generate Excel file directly as HTTP response (no ZIP)"""
        try:
            headers, data = self.prepare_disputes_data(disputes)
            if not data:
                return Response(
                    {"error": "No dispute data found"}, 
                    status=status.HTTP_404_NOT_FOUND
                )

            df = pd.DataFrame(data, columns=headers)
            excel_buffer = BytesIO()

            with pd.ExcelWriter(excel_buffer, engine='xlsxwriter') as writer:
                df.to_excel(writer, index=False, sheet_name='Disputes Data')
            
            excel_buffer.seek(0)
            
            response = HttpResponse(
                excel_buffer.getvalue(),
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            filename = f'disputes_data_{timestamp}.xlsx'
            response['Content-Disposition'] = f'attachment; filename="{filename}"'
            response['Content-Length'] = len(excel_buffer.getvalue())
            
            return response

        except Exception as e:
            return Response(
                {"error": f"Excel generation failed: {str(e)}"}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def add_excel_report_to_zip(self, zip_file, disputes):
        """Add Excel report (optional for speed)"""
        try:
            headers, data = self.prepare_disputes_data(disputes)
            if not data:
                return

            df = pd.DataFrame(data, columns=headers)
            excel_buffer = BytesIO()

            with pd.ExcelWriter(excel_buffer, engine='xlsxwriter') as writer:
                df.to_excel(writer, index=False, sheet_name='Disputes Data')

            excel_buffer.seek(0)
            zip_file.writestr('disputes_data.xlsx', excel_buffer.getvalue())

        except Exception:
            pass

    def prepare_disputes_data(self, disputes):
        headers = [
            'Dispute ID', 'Loan ID', 'Client Name', 'Claimant', 'Respondent', 
            'Arbitrator Name', 'Campaign ID', 'Campaign Name', 'Created Date'
        ]
        data = []

        for dispute in disputes:
            try:
                dispute_id = str(dispute.id)
                loan_id = str(dispute.loan_id) if dispute.loan_id else "N/A"

                # Client Name
                client_name = "N/A"
                if dispute.client and dispute.client.user:
                    first_name = dispute.client.user.first_name or ""
                    last_name = dispute.client.user.last_name or ""
                    client_name = f"{first_name} {last_name}".strip() or "N/A"

                # Claimant
                claimant = "N/A"
                if hasattr(dispute, 'claimant') and dispute.claimant and dispute.claimant.user:
                    first_name = dispute.claimant.user.first_name or ""
                    last_name = dispute.claimant.user.last_name or ""
                    claimant = f"{first_name} {last_name}".strip() or "N/A"

                # Respondent
                respondent = "N/A"
                if hasattr(dispute, 'respondents_name') and dispute.respondents_name:
                    respondent = dispute.respondents_name

                # Arbitrator Name
                arbitrator_name = "N/A"
                arbitrator = dispute.arbitrator_rv.first()
                if arbitrator and arbitrator.user:
                    first_name = arbitrator.user.first_name or ""
                    last_name = arbitrator.user.last_name or ""
                    arbitrator_name = f"{first_name} {last_name}".strip() or "N/A"

                # Campaign info
                campaign_id = str(dispute.campaign.id) if dispute.campaign else "N/A"
                campaign_name = dispute.campaign.excel_file_name if dispute.campaign else "N/A"
                # Created date
                created_date = dispute.created.strftime('%Y-%m-%d %H:%M:%S') if dispute.created else "N/A"

                data.append([
                    dispute_id, loan_id, client_name, claimant, respondent,
                    arbitrator_name, campaign_id, campaign_name, created_date
                ])

            except Exception:
                data.append([str(dispute.id), "Error", "Error", "Error", "Error", "Error", "Error", "Error", "Error"])
        return headers, data

class MasterExcelExportView(APIView):
    permission_classes = [IsAuthenticated, IsAdminOrClient]

    def __init__(self):
        super().__init__()
        # Set timezone offset for IST (UTC+5:30)
        self.timezone_offset = timedelta(hours=5, minutes=30)
        self.timezone_name = "IST (UTC+5:30)"

    def post(self, request):
        # Extract filter values directly from query parameters
        loan_id = request.query_params.get('loan_id')
        case_id = request.query_params.get('case_id')
        client_id = request.query_params.get('client_id')
        campaign_id = request.query_params.get('campaign_id')
        client_name = request.query_params.get('client_name')
        claimant_name = request.query_params.get('claimant_name')
        respondent = request.query_params.get('respondent')
        arbitrator_name = request.query_params.get('arbitrator_name')
        arbitrator_id = request.query_params.get('arbitrator_id')
        case_manager_id = request.query_params.get('case_manager_id')
        export_format = request.query_params.get('export_format', 'excel')

        user = request.user
        current_user_profile = Profile.objects.get(user=user)
        current_user_profile_type = current_user_profile.profile_type

        disputes_filter_conditions = {}

        # Apply user permission filters
        if current_user_profile_type == ProfileType.admin.name:
            pass  # Admin can access all
        elif current_user_profile_type == ProfileType.client.name:
            disputes_filter_conditions['client'] = current_user_profile
        elif current_user_profile_type == ProfileType.sub_client.name:
            disputes_filter_conditions['client'] = current_user_profile.parent_client
        elif current_user_profile_type == ProfileType.case_manager.name:
            disputes_filter_conditions['case_manager_rv'] = current_user_profile
        elif current_user_profile_type == ProfileType.arbitrator.name:
            disputes_filter_conditions['arbitrator_rv'] = current_user_profile
        else:
            return Response({'error': 'Unauthorized'}, status=status.HTTP_403_FORBIDDEN)

        # Apply filters directly from query parameters
        if loan_id:
            disputes_filter_conditions['loan_id'] = loan_id

        if case_id:
            if not case_id.isnumeric():
                return Response({'error': 'Case ID must be a number'}, status=status.HTTP_400_BAD_REQUEST)
            disputes_filter_conditions['id'] = case_id
        campaign = None
        if campaign_id:
            try:
                campaign = Campaign.objects.get(id=campaign_id)
                if campaign.master_excel_generation == 'processing':
                    return Response({'message': 'Master Excel generation is already processing.'}, status=202)
                campaign.master_excel_generation = 'processing'
                campaign.save()
            except Campaign.DoesNotExist:
                return Response({'error': 'Campaign not found'}, status=status.HTTP_404_NOT_FOUND)

        # Build filter conditions based on user permissions and parameters
        disputes_filter_conditions = self._build_filter_conditions(
            current_user_profile, current_user_profile_type, 
            loan_id, case_id, campaign_id, client_id, case_manager_id, arbitrator_id,
            client_name, claimant_name, respondent, arbitrator_name
        )

        # Optimized query with comprehensive prefetching
        disputes = Dispute.objects.filter(**disputes_filter_conditions).select_related(
            'client__user', 'campaign'
        ).prefetch_related(
            'case_manager_rv__user',
            'arbitrator_rv__user', 
            'claimant__user',
            Prefetch('notice_set', queryset=Notice.objects.select_related('template'))
        ).order_by('-created')

        if not disputes.exists():
            return Response({'error': 'No disputes found with given filters or insufficient permissions'},
                          status=status.HTTP_404_NOT_FOUND)

        try:
            if export_format.lower() == 'excel':
                response = self.export_super_excel(disputes, campaign_id, request)
                if campaign:
                    campaign.master_excel_generation = 'completed'
                    campaign.master_excel_generated = True
                    campaign.save()
                return response
            else:
                return Response({'error': 'Unsupported export format. Use "excel"'},
                              status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            if campaign:
                campaign.master_excel_generation = 'pending'
                campaign.save()
            print(f"ERROR: Export failed - {str(e)}")
            traceback.print_exc()
            return Response({"error": f"Excel export failed: {str(e)}"}, status=500)

    def _build_filter_conditions(self, user_profile, user_type, loan_id, case_id, campaign_id, 
                                client_id, case_manager_id, arbitrator_id, client_name, 
                                claimant_name, respondent, arbitrator_name):
        """Build filter conditions for disputes query"""
        conditions = {}

        # Apply user permission filters
        if user_type == ProfileType.admin.name:
            pass  # Admin can access all
        elif user_type in [ProfileType.client.name, ProfileType.sub_client.name]:
            conditions['client'] = user_profile
        elif user_type == ProfileType.case_manager.name:
            conditions['case_manager_rv'] = user_profile
        elif user_type == ProfileType.arbitrator.name:
            conditions['arbitrator_rv'] = user_profile

        # Apply parameter filters
        if loan_id:
            conditions['loan_id'] = loan_id
        if case_id and case_id.isnumeric():
            conditions['id'] = case_id
        if campaign_id and campaign_id.isnumeric():
            conditions['campaign_id'] = campaign_id
        if client_id and client_id.isnumeric():
            conditions['client__user__id'] = client_id
        if case_manager_id and case_manager_id.isnumeric():
            try:
                cm_profile = Profile.objects.get(user_id=int(case_manager_id))
                conditions['case_manager_rv__id'] = cm_profile.id
            except Profile.DoesNotExist:
                pass
        if arbitrator_id and arbitrator_id.isnumeric():
            try:
                arb_profile = Profile.objects.get(user_id=int(arbitrator_id))
                conditions['arbitrator_rv__id'] = arb_profile.id
            except Profile.DoesNotExist:
                pass
        if client_name:
            conditions['client__user__first_name__icontains'] = client_name
        if claimant_name:
            conditions['claimant__user__first_name__icontains'] = claimant_name
        if respondent:
            conditions['respondents_name__icontains'] = respondent
        if arbitrator_name:
            conditions['arbitrator_rv__user__first_name__icontains'] = arbitrator_name

        return conditions

    def get_uploaded_excel_data(self, campaign_id, user):
        """Get uploaded Excel data for campaign"""
        try:
            if not campaign_id:
                return None, None
                
            campaign = get_object_or_404(Campaign, pk=campaign_id)
            user_profile = Profile.objects.get(user=user)
            
            # Permission check
            if (user_profile.profile_type == ProfileType.client.name and 
                not user_profile.profile_type == ProfileType.admin.name):
                if not campaign.client or campaign.client.id != user_profile.id:
                    return None, None
            
            if not campaign.excel_file_name:
                return None, None
            
            # Fetch and process Excel file
            signed_url = gcs_manager.generate_signed_url(campaign.excel_file_name)
            response = requests.get(signed_url, timeout=30)
            response.raise_for_status()
            
            excel_data = BytesIO(response.content)
            df = pd.read_excel(excel_data, engine='openpyxl')
            df = df.replace([float('inf'), float('-inf')], 'N/A').fillna('N/A')
            
            return df.columns.tolist(), df.values.tolist()

        except Exception as e:
            print(f"ERROR: Failed to fetch uploaded Excel data: {str(e)}")
            return None, None

    def merge_excel_data(self, generated_headers, generated_data, uploaded_headers, uploaded_data):
        """Merge generated data with uploaded Excel data"""
        if not uploaded_headers or not uploaded_data:
            return generated_headers, generated_data

        try:
            # Create merged headers
            merged_headers = uploaded_headers.copy()
            merged_headers.extend(generated_headers)

            # Create lookup dictionary for uploaded data
            uploaded_lookup = {}
            case_id_col = loan_id_col = None

            # Find Case ID and Loan ID columns
            for i, header in enumerate(uploaded_headers):
                header_lower = str(header).lower()
                if 'case' in header_lower and 'id' in header_lower and case_id_col is None:
                    case_id_col = i
                elif 'loan' in header_lower and 'id' in header_lower and loan_id_col is None:
                    loan_id_col = i

            # Build lookup dictionary
            for row in uploaded_data:
                if len(row) > 0:
                    if case_id_col is not None and case_id_col < len(row):
                        key = str(row[case_id_col]).strip()
                        if key and key != 'nan':
                            uploaded_lookup[f"case_{key}"] = row
                    if loan_id_col is not None and loan_id_col < len(row):
                        key = str(row[loan_id_col]).strip()
                        if key and key != 'nan':
                            uploaded_lookup[f"loan_{key}"] = row

            # Merge data rows
            merged_data = []
            for generated_row in generated_data:
                uploaded_row = None
                
                # Try to match by Case ID or Loan ID
                if len(generated_row) > 0:
                    case_id = str(generated_row[0]).strip()
                    uploaded_row = uploaded_lookup.get(f"case_{case_id}")
                
                if not uploaded_row and len(generated_row) > 1:
                    loan_id = str(generated_row[1]).strip()
                    uploaded_row = uploaded_lookup.get(f"loan_{loan_id}")

                # Combine uploaded and generated data
                merged_row = uploaded_row.copy() if uploaded_row else ["N/A"] * len(uploaded_headers)
                merged_row.extend(generated_row)
                merged_data.append(merged_row)

            return merged_headers, merged_data

        except Exception as e:
            print(f"ERROR: Failed to merge Excel data: {str(e)}")
            return generated_headers, generated_data

    def format_datetime(self, dt):
        """Format datetime with timezone conversion"""
        if dt is None:
            return "N/A"
        try:
            if timezone.is_naive(dt):
                dt = timezone.make_aware(dt, timezone=timezone.utc)
            local_dt = dt + self.timezone_offset
            return local_dt.strftime('%d-%m-%Y %H:%M')
        except Exception:
            return "Invalid Date"

    def get_campaign_templates_with_usage(self, disputes, client_profile, campaign_id):
        """Get templates associated with campaign that have actual usage evidence"""
        if not campaign_id:
            return []

        # Get campaign templates
        campaign_template_ids = set(CampaignTemplate.objects.filter(
            campaign_id=campaign_id
        ).values_list('template_id', flat=True))

        if not campaign_template_ids:
            return []

        # Get client templates that match campaign templates
        client_templates = Template.objects.filter(
            profile=client_profile,
            id__in=campaign_template_ids,
            name__isnull=False
        ).exclude(name__exact='').select_related('email_template', 'whatsapp_template')

        # Build template definitions with usage evidence check
        template_definitions = []
        dispute_ids = list(disputes.values_list('id', flat=True))

        for template in client_templates:
            template_name = template.name.strip()
            
            # Check for actual usage evidence
            has_notice = Notice.objects.filter(
                dispute__in=disputes, template_id=template.id
            ).exists()
            
            has_entry = Entry.objects.filter(
                dispute__in=disputes, template_id=template.id
            ).exists()
            
            has_email = SendgridMail.objects.filter(
                dispute__in=disputes, template_id=template.id
            ).exists()

            # Only include templates with evidence
            if has_notice or has_entry or has_email:
                # Add email version if exists
                if template.email_template_id and (has_notice or has_email):
                    template_definitions.append({
                        'name': f"Email/{template_name}",
                        'template_id': template.id,
                        'template_name': template_name,
                        'type': 'email'
                    })
                
                # Add WhatsApp version if exists
                if template.whatsapp_template_id and (has_notice or has_entry):
                    template_definitions.append({
                        'name': f"WhatsApp/{template_name}",
                        'template_id': template.id,
                        'template_name': template_name,
                        'type': 'whatsapp'
                    })

        print(f"DEBUG: Found {len(template_definitions)} templates with usage evidence")
        return template_definitions

    def get_template_delivery_status(self, dispute, template_def):
        """Get delivery status for a specific template and dispute"""
        template_id = template_def['template_id']
        template_type = template_def['type']

        try:
            # Check for Notice (with attachment)
            notice = Notice.objects.filter(
                dispute=dispute, template_id=template_id
            ).order_by('-created_at').first()

            if notice:
                # Get delivery confirmation
                if template_type == 'whatsapp':
                    sent_time, read_time = self._get_whatsapp_read_time(dispute, template_id)
                else:
                    sent_time, read_time = self._get_email_read_time(dispute, template_id)
                
                if read_time:
                    return f"Sent (w/ attachment): {sent_time} | Read: {read_time}"
                return f"Sent (w/ attachment): {sent_time}"

            # Check for delivery without attachment
            if template_type == 'whatsapp':
                sent_time, read_time = self._get_whatsapp_read_time(dispute, template_id)
                if read_time:
                    return f"Sent (no attachment): {sent_time} | Read: {read_time}"
                return f"Sent (no attachment): {sent_time}"

            elif template_type == 'email':
                sent_time, read_time = self._get_email_read_time(dispute, template_id)
                if read_time:
                    return f"Sent (no attachment): {sent_time} | Read: {read_time}"
                return f"Sent (no attachment): {sent_time}"

            return "Not Sent"

        except Exception as e:
            print(f"ERROR: Status check failed for template {template_id}: {str(e)}")
            return "Error"

    def _get_whatsapp_read_time(self, dispute, template_id):
        """Get WhatsApp read timestamp for specific template"""
        try:
            initiated_entry = Entry.objects.filter(
                dispute=dispute, template_id=template_id, status='initiated'
            ).first()
            
            if initiated_entry:
                read_entry = Entry.objects.filter(
                    msg_id=initiated_entry.msg_id, status='read'
                ).first()
                
                if read_entry:
                    return self.format_datetime(initiated_entry.timestamp), self.format_datetime(read_entry.timestamp)
                return self.format_datetime(initiated_entry.timestamp), None
            return None, None
        except Exception:
            return None, None

    def _get_email_read_time(self, dispute, template_id):
        """Get email read timestamp for specific template"""
        try:
            email_entry = SendgridMail.objects.filter(
                dispute=dispute, template_id=template_id
            ).first()
            
            if email_entry:
                read_entry = SendgridEventEntry.objects.filter(
                    sendgrid_mail=email_entry, event='open'
                ).first()
                
                if read_entry:
                    return self.format_datetime(email_entry.timestamp), self.format_datetime(read_entry.timestamp)
                return self.format_datetime(email_entry.timestamp), None
            return None, None
        except Exception:
            return None, None

    def generate_signed_url_for_dispute_template(self, dispute, template_id, template_name):
        """Generate signed URL for PDF file"""
        try:
            if not dispute.loan_id or not dispute.campaign_id:
                return "No Loan ID" if not dispute.loan_id else "No Campaign"
            
            file_path = f'notices/{dispute.campaign_id}/{template_id}/{template_name}/{dispute.loan_id}.pdf'
            return gcs_manager.generate_signed_url(file_path, expiration_seconds=3600*24*7)
        except Exception:
            return "URL Error"

    def prepare_excel_data(self, disputes, campaign_id):
        """Prepare data for Excel export - includes ALL disputes"""
        if not disputes.exists():
            return None, None

        client_profile = disputes.first().client
        if not client_profile:
            return None, None

        # Get templates with actual usage evidence
        template_definitions = self.get_campaign_templates_with_usage(
            disputes, client_profile, campaign_id
        )

        # Build headers - always include base dispute info
        base_headers = ['Case ID', 'Loan ID', 'Client Name', 'Claimant', 'Respondent', 'Arbitrator Name']
        
        # Add template headers if templates exist
        template_headers = []
        pdf_templates = []
        
        if template_definitions:
            template_headers = [t['name'] for t in template_definitions]
            
            # Add PDF URL columns for templates with attachments
            for template_def in template_definitions:
                if Notice.objects.filter(
                    dispute__in=disputes, 
                    template_id=template_def['template_id']
                ).exists():
                    pdf_templates.append(f"{template_def['template_name']} - PDF URL")

        headers = base_headers + template_headers + pdf_templates + ["Dispute Status"]

        # Build data rows for ALL disputes (not just those with template usage)
        data = []
        for dispute in disputes:
            try:
                row = [
                    str(dispute.id),
                    str(dispute.loan_id) if dispute.loan_id else "N/A",
                    self._get_user_name(dispute.client),
                    self._get_user_name(dispute.claimant),
                    dispute.respondents_name or "N/A",
                    self._get_arbitrator_name(dispute)
                ]

                # Add template status columns (will be "Not Sent" if no template usage)
                for template_def in template_definitions:
                    status = self.get_template_delivery_status(dispute, template_def)
                    row.append(status)

                # Add PDF URLs for templates with attachments (will be empty if no attachment)
                for template_def in template_definitions:
                    if Notice.objects.filter(
                        dispute=dispute, 
                        template_id=template_def['template_id']
                    ).exists():
                        url = self.generate_signed_url_for_dispute_template(
                            dispute, template_def['template_id'], template_def['template_name']
                        )
                        row.append(url)
                    else:
                        row.append("N/A")  # No attachment for this dispute

                # Add dispute status
                row.append(dispute.status or "N/A")
                data.append(row)

            except Exception as e:
                print(f"ERROR: Failed to process dispute {dispute.id}: {str(e)}")
                continue

        # Return data even if no templates found (will just have base dispute info)
        return headers, data

    def _get_user_name(self, profile):
        """Get full name from profile"""
        if not profile or not profile.user:
            return "N/A"
        first_name = profile.user.first_name or ""
        last_name = profile.user.last_name or ""
        return f"{first_name} {last_name}".strip() or "N/A"

    def _get_arbitrator_name(self, dispute):
        """Get arbitrator name from dispute"""
        arbitrators = list(dispute.arbitrator_rv.all())
        if arbitrators and arbitrators[0].user:
            return self._get_user_name(arbitrators[0])
        return "N/A"

    def export_super_excel(self, disputes, campaign_id=None, request=None):
        """Export disputes to Excel format - includes ALL disputes"""
        try:
            headers, data = self.prepare_excel_data(disputes, campaign_id)
            
            # Changed condition: return error only if no disputes at all, not if no template data
            if not headers or not data:
                return Response({
                    "error": "No disputes found to export"
                }, status=404)

            # Merge with uploaded Excel data if available
            if campaign_id and request:
                uploaded_headers, uploaded_data = self.get_uploaded_excel_data(campaign_id, request.user)
                if uploaded_headers and uploaded_data:
                    headers, data = self.merge_excel_data(headers, data, uploaded_headers, uploaded_data)

            # Create Excel file
            df = pd.DataFrame(data, columns=headers)
            df = df.replace([float('inf'), float('-inf')], 'N/A').fillna('N/A')
            
            output = BytesIO()
            with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
                df.to_excel(writer, index=False, sheet_name='Campaign Report')
                
                # Format the worksheet
                workbook = writer.book
                worksheet = writer.sheets['Campaign Report']
                
                # Auto-adjust column widths and make URLs clickable
                for i, col in enumerate(headers):
                    max_length = max(len(str(col)), 20)
                    if 'PDF URL' in str(col):
                        max_length = 80
                        # Make URLs clickable
                        for row_num in range(1, len(data) + 1):
                            if i < len(data[row_num - 1]):
                                cell_value = data[row_num - 1][i]
                                if str(cell_value).startswith('http'):
                                    worksheet.write_url(row_num, i, cell_value, string="Download PDF")
                    elif i >= 6:  # Template columns
                        max_length = 50
                    
                    worksheet.set_column(i, i, max_length)
                
                # Freeze header row
                worksheet.freeze_panes(1, 0)
                
                # Add metadata
                metadata_row = len(data) + 2
                metadata_format = workbook.add_format({'italic': True, 'font_size': 10})
                worksheet.write(metadata_row, 0, f"Generated: {self.format_datetime(timezone.now())}", metadata_format)
                worksheet.write(metadata_row + 1, 0, f"Total Records: {len(data)}", metadata_format)
                worksheet.write(metadata_row + 2, 0, f"Campaign ID: {campaign_id}", metadata_format)

            output.seek(0)

            # Store file in GCS
            filename = f'master_case_files_campaign_{campaign_id}.xlsx'
            store_path = f"notices/{campaign_id}/reports/{filename}"
            gcs_manager.upload_file(store_path, output)

            # Update campaign status
            if campaign_id:
                try:
                    campaign = Campaign.objects.get(id=campaign_id)
                    campaign.master_excel_generated = True
                    campaign.save()
                except Campaign.DoesNotExist:
                    pass

            return Response({
                "message": "Report generation completed successfully.",
                "file_path": store_path,
                "total_records": len(data)
            }, status=200)

        except Exception as e:
            print(f"ERROR: Excel export failed - {str(e)}")
            traceback.print_exc()
            return Response({"error": f"Excel export failed: {str(e)}"}, status=500)


class DownloadMasterExcelReportView(APIView):
    permission_classes = [IsAuthenticated, IsAdminOrClient]

    def get(self, request, campaign_id=None):
        if not campaign_id:
            return Response({'error': 'campaign_id is required'}, status=status.HTTP_400_BAD_REQUEST)
        
        campaign: Campaign = get_object_or_404(Campaign,id=campaign_id)

        user=request.user
        current_profile = get_object_or_404(Profile,user=user)
        if not validate_profile_campaign(current_profile,campaign):
            return Response({'error': 'Not authorized to perfrorm this operation'}, status=status.HTTP_403_FORBIDDEN)

        try:
            blob_name = f'notices/{campaign_id}/reports/master_case_files_campaign_{campaign_id}.xlsx'
            if not gcs_manager.file_exists(blob_name):
                return Response({'error': 'Report file not found'}, status=400)

            # Generate signed URL for the report file
            signed_url = gcs_manager.generate_signed_url(blob_name)
            response = {
                'file_name': blob_name.split('/')[-1],
                'url': signed_url
            }
            if not response:
                return Response({'error': 'No files found in reports folder.'}, status=404)
            return Response({'files': response}, status=200)
        except Exception as e:
            return Response({'error': f'Error retrieving report files: {str(e)}'}, status=500)


class DisputeUpdateView(APIView):

    permission_classes = [IsAuthenticated, IsAdminOrClient]    

    def post(self,request) -> Response:

        action = request.data.get("action")

        data = request.data.get("data")
        if not data:
            return Response('No Valid data',status=status.HTTP_400_BAD_REQUEST)
        
        dispute_ids = data['ids']
        if not dispute_ids:
            return Response('No dispute IDs sent',status=status.HTTP_400_BAD_REQUEST)
        
        user = request.user
        current_user_profile = get_object_or_404(Profile,user=user)
        current_user_profile_type = current_user_profile.profile_type        
        
        current_profile = Profile.objects.get(user=user)
        current_profile_type = current_profile.profile_type

        if current_profile_type == ProfileType.admin.name:
            pass
        elif current_profile_type == ProfileType.client.name:
            if current_profile.id not in Dispute.objects.filter(id__in=dispute_ids).values_list('client__id',flat=True):
                return Response({"error":'Not authorized to update disputes'},status=status.HTTP_403_FORBIDDEN)
        elif current_profile_type == ProfileType.sub_client.name:
            if current_profile.parent_client.id not in Dispute.objects.filter(id__in=dispute_ids).values_list('client__id',flat=True):
                return Response({"error":'Not authorized to update disputes'},status=status.HTTP_403_FORBIDDEN)
        elif current_profile_type == ProfileType.case_manager.name:
            if current_profile.id not in Dispute.objects.filter(id__in=dispute_ids).values_list('case_manager_rv__id',flat=True):
                return Response({"error":'Not authorized to update disputes'},status=status.HTTP_403_FORBIDDEN)
        elif current_profile_type == ProfileType.arbitrator.name:
            return Response({"error":'Not authorized to update disputes'},status=status.HTTP_403_FORBIDDEN)
        
        filter_conditions = dict()
        if action=="close_dispute":            
            
            selected_disputes = Dispute.objects.filter(id__in = dispute_ids,status = 'closed')
            if selected_disputes:
                return Response({"error":"Some disputes are already closed. Please check again"},status=status.HTTP_400_BAD_REQUEST)
            filter_conditions['status'] = 'closed'
            filter_conditions['closed_reason'] = data["closure_reason"]
            filter_conditions['closed_date'] = datetime.now()    
            filter_conditions['closed_by'] = current_user_profile
            try:
                Dispute.objects.filter(id__in = dispute_ids).update(**filter_conditions)
            except Exception as e:
                print(f'Exception encountered while updating disputes',str(e))
                return Response({"error":'Error updating disputes'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
        if action=="update_claimant":          
            
            try:
                Dispute.objects.filter(id__in = dispute_ids).update(claimant_id=data['claimant_id'])
            except Exception as e:
                print(f'Exception encountered while updating disputes',str(e))
                return Response('Error updating disputes', status=status.HTTP_500_INTERNAL_SERVER_ERROR)           
        

        return Response('Disputes updated successfully',status=status.HTTP_200_OK)

