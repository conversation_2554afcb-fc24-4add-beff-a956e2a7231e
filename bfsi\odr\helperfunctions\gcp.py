import json
import os
from typing import Any, Dict

from google.auth.exceptions import GoogleAuthError
from google.cloud import secretmanager


class GCPCredentialsManager:
    """
    Manages GCP credentials and secret retrieval with enhanced error handling and flexibility.
    """

    @staticmethod
    def setup_gcp_credentials() -> Dict[str, Any]:
        """
        Retrieve and validate GCP credentials from environment variables.

        Returns:
            Dict containing GCP credentials

        Raises:
            EnvironmentError: If credentials are not set
            ValueError: If credentials are in invalid format
        """
        # Determine the appropriate environment variable based on the current environment
        env = os.getenv('ENV', 'dev').lower()
        credentials_env_var = 'GCP_PROD_CREDENTIALS' if env in [
            'prod', 'hotstar-prod'] else 'GCP_DEV_CREDENTIALS'

        # Retrieve credentials
        credentials_json = os.getenv(credentials_env_var, "").strip()

        if not credentials_json:
            raise EnvironmentError(
                f"{credentials_env_var} environment variable is not set.")

        try:
            # Validate JSON structure
            credentials_dict = json.loads(credentials_json)

            # Validate required keys
            required_keys = ['project_id', 'client_email', 'private_key']
            for key in required_keys:
                if key not in credentials_dict:
                    raise ValueError(f"Missing required key: {key}")

            print('Fetched credentials')
            return credentials_dict

        except json.JSONDecodeError as e:
            raise ValueError(
                f"Invalid JSON in {credentials_env_var} environment variable.") from e

    @staticmethod
    def get_secret_values(credentials_dict: Dict[str, Any], secret_name: str) -> Dict[str, Any]:
        """
        Retrieve secret values from Google Cloud Secret Manager.

        Args:
            credentials_dict: GCP credentials dictionary
            secret_name: Name of the secret to retrieve

        Returns:
            Dict containing secret values

        Raises:
            ValueError: If project ID is missing or secret retrieval fails
        """
        try:
            project_id = credentials_dict.get("project_id")
            if not project_id:
                raise ValueError("Project ID is missing from credentials.")

            # Create Secret Manager client
            secret_client = secretmanager.SecretManagerServiceClient.from_service_account_info(
                credentials_dict)

            # Construct secret path
            secret_path = f"projects/{project_id}/secrets/{secret_name}/versions/latest"

            # Access and decode secret
            response = secret_client.access_secret_version(name=secret_path)
            secret_values_string = response.payload.data.decode('UTF-8')

            print('Fetched secrets\n')
            return json.loads(secret_values_string, strict=False)

        except (GoogleAuthError, secretmanager.types.AccessSecretVersionResponse.Error) as e:
            raise ValueError(
                f"Failed to retrieve secret {secret_name}: {str(e)}") from e
