from io import BytesIO
import logging
from rest_framework.exceptions import ValidationError
import pandas as pd
from django.utils import timezone
import os


db_logger = logging.getLogger('db')

def add_dispute_mode_to_profile(host, profile):
    mode = host.upper()

    if profile.dispute_modes:
        if mode not in profile.dispute_modes:
            profile.dispute_modes = profile.dispute_modes + ' | ' + mode
            profile.save()
    else:
        profile.dispute_modes = mode
        profile.save()


def getPlatformLinkByUrl(url="///"):
    link = ""
    arr = str(url).split('/')
    if len(arr) < 2:  
        return link 
    print('arr', arr)
    if arr[2] == 'odr.webnyay.in':      # cdr
        link = "https://odr.webnyay.in/"
    else:
        pass

    return link


# Utility function to validate Excel file
def validate_and_normalize_excel_file(file):
    """
    Validate Excel file format and normalize column names, return DataFrame and normalized Excel file object.
    Only checks if required columns are present, doesn't validate data completeness.
    """
    if not file.name.endswith('.xlsx'):
        raise ValidationError("Only Excel files with '.xlsx' format are allowed.")
    try:
        df = pd.read_excel(file)

        # Define required columns (exact case and spacing as desired in final output)
        required_columns_standard = [
            'Name of the Borrower',
            'Borrower\'s Email', 
            'Borrower\'s Number',
            'Loan ID'
        ]

        # Create mapping for case-insensitive and whitespace-trimmed matching
        column_mapping = create_column_mapping(df.columns, required_columns_standard)

        # Check if all required columns are found
        missing_columns = []
        for std_col in required_columns_standard:
            if std_col not in column_mapping.values():
                missing_columns.append(std_col)

        if missing_columns:
            raise ValidationError(f"Missing required columns: {', '.join(missing_columns)}")

        #Loan ids must match their corresponding pdf names. If names have '/', it will not get uploaded to storage correctly.
        #So raise this error so that CMs can replace / with some other character.
        loan_ids_with_slashes = [id for id in df['Loan ID'] if '/' in id]
        if loan_ids_with_slashes:            
            raise ValidationError(f"Loan Ids cannot have slash(/) in the names: {', '.join(loan_ids_with_slashes)}")

        # Rename columns to standard format
        df = df.rename(columns=column_mapping)

        # Create normalized Excel file object
        normalized_excel_file = create_normalized_excel_file_object(df)

        return df, normalized_excel_file

    except Exception as e:
        if isinstance(e, ValidationError):
            raise
        raise ValidationError(f"Error processing Excel file: {str(e)}")


def create_column_mapping(original_columns, required_columns_standard):
    """
    Create mapping between original columns (case-insensitive, trimmed) and standard column names.
    Handles different data types that pandas might interpret columns as.
    """
    column_mapping = {}

    # Normalize original columns for comparison - handle different data types
    normalized_original = {}
    for col in original_columns:
        # Convert to string first, then normalize
        col_str = str(col).strip().lower()
        normalized_original[col_str] = col

    # Create mapping for each required column
    for std_col in required_columns_standard:
        std_col_normalized = std_col.strip().lower()

        # Find matching column in original data
        for norm_col, orig_col in normalized_original.items():
            if norm_col == std_col_normalized:
                column_mapping[orig_col] = std_col
                break

    return column_mapping


def create_normalized_excel_file_object(df):
    """
    Create a normalized Excel file with standardized column names and return as BytesIO object.
    This object can be used directly with gcs_manager.upload_file().
    """
    excel_buffer = BytesIO()

    # Save DataFrame to Excel in memory
    with pd.ExcelWriter(excel_buffer, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='Sheet1')

    excel_buffer.seek(0)  # Reset pointer to beginning for upload
    return excel_buffer



# Utility function to extract common data from the first row of the Excel sheet
def extract_common_data(df):
    common_fields = {
        'address': 'Borrower\'s Address',
        'pincode': 'Pincode',
        'company_name': 'Company Name',
        'case_manager_name': 'Case Manager\'s Name',
        'case_manager_email': 'Case Manager\'s Email',
        'case_manager_phone': 'Case Manager\'s Phone No.'
    }
    return {key: df.iloc[0].get(column) for key, column in common_fields.items()}


def get_timestamped_filename(filename):
    name, ext = os.path.splitext(filename)
    timestamp = timezone.now().strftime("%Y%m%d%H%M%S")
    return f"{name}_{timestamp}{ext}" 