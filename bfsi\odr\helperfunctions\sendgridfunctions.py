import base64
import datetime
import json
import pytz
import sendgrid
from django.utils import timezone
from sendgrid.helpers.mail import Attachment as SendGridAttachment
from sendgrid.helpers.mail import (Bcc, Category, Cc, ContentId, Disposition,
                                   FileContent, FileName, FileType, Mail,
                                   Personalization, To)

from odr.helperfunctions.databasefunctions import is_production_database
from sendgridlogs.models import SendgridMail
from bfsi.settings import (DB_NAME, MAIL_ID,
                            SEND_EMAIL, SENDGRID_API_KEY, TIME_ZONE)

sendgrid_client = sendgrid.SendGridAPIClient(api_key=SENDGRID_API_KEY)


def sendTemplateEmail(to, template_id, substition_data={}, tiac=None, custom_args={},cc_emails=[],bcc_emails=[]):
    if not SEND_EMAIL:
        return 'email not sent'
    from_email = MAIL_ID
    message = Mail(
        from_email=from_email,
    )
    personalization = Personalization()
    personalization.add_to(To(to))
    for cc_email in cc_emails:
        personalization.add_cc(Cc(cc_email))
    for bcc_email in bcc_emails:  # Assuming `bcc_emails` is a list
        personalization.add_bcc(Bcc(bcc_email))
    message.add_personalization(personalization)
    message.dynamic_template_data = substition_data
    message.template_id = template_id
    message.dispute_id = custom_args.get('dispute_id')
    message.category = [Category(DB_NAME)]
    return send_sendgrid_message_and_save_to_database(message, to)


def sendTemplateEmailDIFC(to, template_id, substition_data={}, custom_args={}):
    if not SEND_EMAIL:
        return 'email not sent'
    if is_production_database():
        recips = ["<EMAIL>"]
    else:
        recips = ['<EMAIL>']
    from_email = MAIL_ID
    message = Mail(
        from_email=from_email,
    )
    personalization = Personalization()
    personalization.add_to(To(to))
    for bcc_addr in recips:
        personalization.add_bcc(Bcc(bcc_addr))
    message.add_personalization(personalization)
    message.dynamic_template_data = substition_data
    message.template_id = template_id
    message.dispute_id = custom_args.get('dispute_id')
    message.category = [Category(DB_NAME)]
    return send_sendgrid_message_and_save_to_database(message, to)


def send_email_template(to_email, template_id, substition_data={}, from_email=MAIL_ID, files=[], cced_emails=[], bcced_emails=[], custom_args={}):
    if not SEND_EMAIL:
        return 'email not sent'
    message = Mail(
        from_email=from_email,
    )
    personalization = Personalization()
    personalization.add_to(To(to_email))
    for bcc_addr in bcced_emails:
        personalization.add_bcc(Bcc(bcc_addr))
    for ccE in cced_emails:
        personalization.add_cc(Cc(ccE))
    message.add_personalization(personalization)
    message.dynamic_template_data = substition_data
    message.template_id = template_id
    attachments = []
    for file in files:
        encoded = base64.b64encode(file['file']).decode()
        attachment = SendGridAttachment()
        attachment.file_content = FileContent(encoded)
        attachment.file_name = FileName(file['name'])
        attachment.disposition = Disposition('attachment')
        attachment.content_id = ContentId(''+file['name'])
        attachments.append(attachment)
    message.attachment = attachments
    message.dispute_id = custom_args.get('dispute_id')
    message.category = [Category(DB_NAME)]
    return send_sendgrid_message_and_save_to_database(message, to_email)


def get_subject_from_template(template_id):
    try:
        response = sendgrid_client.client.templates._(template_id).get()
        decoded_body = response.body.decode('utf-8')
        template_info = json.loads(decoded_body)
        # Extract subject from the template_info JSON
        subject = template_info.get('versions', [{}])[0].get('subject')
        return subject
    except Exception as e:
        print("Error retrieving subject from template:", e)
        return None


def send_sendgrid_message_and_save_to_database(message, to_email):
    try:
        subject = get_subject_from_template(message.template_id.template_id)
        dispute_id = message.dispute_id
        response = sendgrid_client.send(message)
        # Retrieve the message_id from the response headers
        x_message_id = response.headers.get('X-Message-Id')
        timestamp = timezone.make_aware(datetime.datetime.now(), pytz.timezone(TIME_ZONE))
        SendgridMail.objects.create(
            x_message_id=x_message_id,
            email=to_email,
            timestamp=timestamp,
            dispute_id=dispute_id,
            subject=subject 
        )
        return f'\nstatus_code {str(response.status_code)}\nbody {str(response.body)}\nheaders {str(response.headers)}'
    except Exception as e:
        # print(e.message)
        print("in excccccccccc", e)
        return '\n' + str(e)


def sendAttchmentEmailTiac(profile, to, file, template_id, file_name, substition_data, tiac=None, custom_args={}):
    if not SEND_EMAIL:
        return 'email not sent'
    recips = []
    f = MAIL_ID
    message = Mail(
        from_email=f,
    )
    if is_production_database():
        if profile:
            recips = ["<EMAIL>", "<EMAIL>"]
    personalization = Personalization()
    personalization.add_to(To(to))
    for bcc_addr in recips:
        personalization.add_bcc(Bcc(bcc_addr))
    message.add_personalization(personalization)
    message.dynamic_template_data = substition_data
    message.template_id = template_id
    if file:
        encoded = base64.b64encode(file).decode()
        attachment = SendGridAttachment()
        attachment.file_content = FileContent(encoded)
        attachment.file_type = FileType('application/pdf')
        attachment.file_name = FileName(file_name)
        attachment.disposition = Disposition('attachment')
        attachment.content_id = ContentId('Example Content ID')
        message.attachment = attachment
    message.dispute_id = custom_args.get('dispute_id')
    message.category = [Category(DB_NAME)]
    return send_sendgrid_message_and_save_to_database(message, to)
