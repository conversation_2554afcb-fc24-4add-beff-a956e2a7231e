from django.urls import path
from django.conf.urls import url

from odr.views import *

urlpatterns = [
    path('api/bulk-user-dispute-case/', BulkUserDisputeCaseView.as_view(), name='bulk_user_dispute_case'),
    url(r'^customsignup', CustomUserSignupView.as_view(), name="customsignup"),
    path('api/users/', UserManagementView.as_view(), name='user-list'),  # For listing all users
    path('api/users/<int:id>/', UserManagementView.as_view(), name='user-detail-update'),  # For retrieve, update by ID
    path('api/profile/', ProfileGet.as_view(), name="get_user_profile"),

    # bulk dispute creation trigger
    path('api/bulk-disputes/', TriggerBulkDisputeCampaignView.as_view(), name='bulk-disputes'),
    path('api/bulk-disputes/download-report/<int:campaign_id>/', DownloadBulkDisputeCreationReportView.as_view(), name='download-report'),

    # get campaigns details
    path('api/campaigns/<int:campaign_id>/stream/', campaign_progress_stream, name='campaign_progress_stream'),
    path('api/campaigns/', CampaignListView.as_view(), name='campaign-list'),

    path('api/list-casefiles/', CaseFileListView.as_view(), name='casefile-list'),
    path('api/case-file/<int:pk>/', CaseFileDetailView.as_view(), name='casefile-detail'),
    path('api/get-clients/', ClientProfileListView.as_view(), name='get-clients'),
    path('api/get-claimants/', ClaimantProfileListView.as_view(), name='get-claimant'),
    path('api/key-metrics/', KeyMetricsView.as_view(), name='key-metrics'),
    path('api/casefile-notices/<int:pk>/', DisputeNotices.as_view(), name='dispute-notices'),
    path('api/notice-download/<int:pk>/', NoticesDownloadById.as_view(), name='notice-download'),
    path('api/get-profiles/', GenericProfilesView.as_view(), name='get-profiles'),
    path('api/assign-proffessional/', AssignProfessionalsView.as_view(), name='assign-profiles'),
    path('api/get-excel-by-client/', ClientExcelView.as_view(), name='client-excel'),
    path('api/dashboard-sheets/', DashboardSheetsView.as_view(), name='dashboard-sheets'),
    path('api/excel-download/<int:pk>/', ExcelDownloadById.as_view(), name='excel-download'),
    path('api/dispute-files/download/<int:file_id>/', DisputeFileDownloadById.as_view(), name='file-download'),
    path('api/dispute-upload-file/<int:dispute_id>/', DisputeFileUploadView.as_view(), name='upload-file'),
    path('api/upload-file/<int:campaign_id>/', BulkFileUploadView.as_view(), name='bulk-upload-file'),

    # Get professionals id and name by client or campaign for filter on get list-casefiles
    path('api/professionals-by-client/', GetProfessionalsByClientView.as_view(), name='professionals-by-client'),

    # export zip which includes excel and pdfs of all the selected disputes
    path('api/disputes/export-zip/', DisputeFilesZipExportView.as_view(), name='dispute-files-zip-export'),

    path('api/master-excel-export/', MasterExcelExportView.as_view(), name='master-excel-export'),
    path('api/download-master-excel-report/<int:campaign_id>/', DownloadMasterExcelReportView.as_view(), name='download-master-excel-report'),

    # dispute
    path('api/update-disputes/', DisputeUpdateView.as_view(), name='update-disputes'),

]
