from django.contrib.auth.models import update_last_login
from django.core.exceptions import ValidationError
from django.utils.decorators import method_decorator
# from django_ratelimit.decorators import ratelimit
from rest_framework import serializers, status
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated

from authorization.models import User
from authorization.serializers import SignupSerializer
from odr.models import Profile
# from odr.utils import add_dispute_mode_to_profile
from bfsi.settings import db_logger
from rest_framework.permissions import AllowAny,IsAuthenticated
from rest_framework.generics import CreateAPIView
from rest_framework_simplejwt.exceptions import TokenError
from rest_framework import status, permissions
from rest_framework_simplejwt.tokens import OutstandingToken, BlacklistedToken




class TokenAuthView(APIView):
    permission_classes = []
    authentication_classes = []

    def post(self, request):
        email = request.data.get('email')
        phone_number = request.data.get('phone_number')
        password = request.data.get('password')
        host = self.request.query_params.get('host', '')
        timezone = request.data.get('timezone')

        if not email and not phone_number:
            return Response({'error': 'email or phone_number is required.'},
                            status=status.HTTP_400_BAD_REQUEST)
        if not password:
            return Response({'error': 'password is required.'},
                            status=status.HTTP_400_BAD_REQUEST)

        # get user object by email or phone_number
        try:
            if email:
                user = User.objects.get(email=email)
            else:
                user = User.objects.get(profile__phone_number=phone_number)
        except:
            raise serializers.ValidationError(
                {"non_field_errors": ["Unable to log in with provided credentials."]})

        # check for correct password
        if not user.check_password(password):
            raise serializers.ValidationError(
                {"non_field_errors": ["Unable to log in with provided credentials."]})

        profile = Profile.objects.get(user=user)
        # save timezone for every time user logins
        try:
            if timezone != profile.timezone:
                profile.timezone = timezone
                profile.save()
        except ValidationError:
            db_logger.exception(f"Exception invalid timezone in token auth for user {user.email} - timezone {timezone}")
            pass
        # if host:
        #     add_dispute_mode_to_profile(host, profile)

        # Generate JWT tokens using djangorestframework-simplejwt
        refresh = RefreshToken.for_user(user)
        profile = Profile.objects.get(user=user)

        response_data = {
            'refresh': str(refresh),
            'token': str(refresh.access_token),
            'user': {
                'id': user.id,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'phone_number': profile.phone_number,
                'profile_type': profile.profile_type,
            }
        }

        response = Response(response_data)
        update_last_login(None, user)
        return response


class SignupView(CreateAPIView):
    serializer_class = SignupSerializer
    permission_classes = [AllowAny]


class LogoutView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        user = request.user

        try:
            tokens = OutstandingToken.objects.filter(user=user)
            for token in tokens:
                try:
                    BlacklistedToken.objects.get(token=token)
                except BlacklistedToken.DoesNotExist:
                    BlacklistedToken.objects.create(token=token)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

        return Response({"detail": "Logged out successfully."}, status=status.HTTP_205_RESET_CONTENT)