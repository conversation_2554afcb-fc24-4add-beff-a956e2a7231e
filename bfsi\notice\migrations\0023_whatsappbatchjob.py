# Generated by Django 3.2 on 2025-04-24 12:09

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('notice', '0022_entry_loan_id'),
    ]

    operations = [
        migrations.CreateModel(
            name='WhatsAppBatchJob',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_batches', models.IntegerField(default=0)),
                ('completed_batches', models.IntegerField(default=0)),
                ('total_messages', models.IntegerField(default=0)),
                ('successful_messages', models.IntegerField(default=0)),
                ('failed_messages', models.IntegerField(default=0)),
                ('status', models.CharField(choices=[('processing', 'Processing'), ('completed', 'Completed'), ('failed', 'Failed')], default='processing', max_length=20)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('campaign', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='whatsapp_batch_jobs', to='notice.campaign')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='whatsapp_batch_jobs', to='notice.template')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
