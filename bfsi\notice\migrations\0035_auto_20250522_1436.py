# Generated by Django 3.2 on 2025-05-22 09:06

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('notice', '0034_auto_20250522_1325'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='campaign',
            name='template',
        ),
        migrations.CreateModel(
            name='CampaignTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('campaign', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='notice.campaign')),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='notice.template')),
            ],
        ),
    ]
