from rest_framework import permissions
from django.db.models import Q

from odr.models import Profile, ProfileType

class IsAdmin(permissions.BasePermission):
    """
    Allows access only to Admin.
    """

    def has_permission(self, request, view):
        user = request.user

        try:
            Profile.objects.get(user=user,
                                profile_type=ProfileType.admin.name,)
        except Profile.DoesNotExist:
            return False

        return True

class IsClientAndSelf(permissions.BasePermission):
    """
    Allows access only to client-type users accessing their own data.
    Client types include client, sub_client, case_manager, and arbitrator.
    """
    def has_object_permission(self, request, view, obj):
        user = request.user
        client_type_profiles = [
            ProfileType.client.name,
            ProfileType.sub_client.name,
            ProfileType.case_manager.name,
            ProfileType.arbitrator.name
        ]
        is_client_type = Profile.objects.filter(user=user, profile_type__in=client_type_profiles).exists()
        return is_client_type and obj.user == user


class IsAdminOrClient(permissions.BasePermission):
    """
    Allows access to admin users or client-type users with specific permissions:
    - Admin: Full access to all data
    - Client: Access to their own data
    - Sub_client: Same access as client (to their own data)
    - Case_manager: Access to cases they're assigned to, can create cases, send notices, assign arbitrators and case managers
    - Arbitrator: Access only to cases they're assigned to and related notices
    """
    def has_permission(self, request, view):
        user = request.user
        # Check if user is admin
        is_admin = Profile.objects.filter(user=user, profile_type=ProfileType.admin.name).exists()
        if is_admin:
            return True

        # Check if user is client type
        client_type_profiles = [
            ProfileType.client.name,
            ProfileType.sub_client.name,
            ProfileType.case_manager.name,
            ProfileType.arbitrator.name
        ]
        is_client_type = Profile.objects.filter(user=user, profile_type__in=client_type_profiles).exists()
        return is_client_type

    def has_object_permission(self, request, view, obj):
        user = request.user

        # Admin can access any object
        is_admin = Profile.objects.filter(user=user, profile_type=ProfileType.admin.name).exists()
        if is_admin:
            return True

        # Get user's profile(s)
        try:
            # First check if user has a client or sub_client profile
            if Profile.objects.filter(user=user, profile_type__in=[ProfileType.client.name, ProfileType.sub_client.name]).exists():
                profile = Profile.objects.get(user=user, profile_type__in=[ProfileType.client.name, ProfileType.sub_client.name])
                profile_type = profile.profile_type
            # Then check for case_manager
            elif Profile.objects.filter(user=user, profile_type=ProfileType.case_manager.name).exists():
                profile = Profile.objects.get(user=user, profile_type=ProfileType.case_manager.name)
                profile_type = ProfileType.case_manager.name
            # Finally check for arbitrator
            elif Profile.objects.filter(user=user, profile_type=ProfileType.arbitrator.name).exists():
                profile = Profile.objects.get(user=user, profile_type=ProfileType.arbitrator.name)
                profile_type = ProfileType.arbitrator.name
            else:
                return False
        except Profile.DoesNotExist:
            return False

        # CLIENT & SUB_CLIENT: Check if the object belongs to the client
        if profile_type in [ProfileType.client.name, ProfileType.sub_client.name]:
            # Direct ownership checks
            if hasattr(obj, 'client') and obj.client == profile:
                return True
            elif hasattr(obj, 'client_id') and obj.client_id == profile.id:
                return True
            elif hasattr(obj, 'profile') and obj.profile == profile:
                return True
            elif hasattr(obj, 'profile_id') and obj.profile_id == profile.id:
                return True

            # For dispute-related objects
            if hasattr(obj, 'dispute'):
                if hasattr(obj.dispute, 'client') and obj.dispute.client == profile:
                    return True
                elif hasattr(obj.dispute, 'client_id') and obj.dispute.client_id == profile.id:
                    return True

            # For campaign-related objects
            if hasattr(obj, 'campaign'):
                if hasattr(obj.campaign, 'client') and obj.campaign.client == profile:
                    return True
                elif hasattr(obj.campaign, 'client_id') and obj.campaign.client_id == profile.id:
                    return True

        # CASE MANAGER: Check if the case manager is assigned to the dispute
        elif profile_type == ProfileType.case_manager.name:
            # Direct case manager assignment check
            if hasattr(obj, 'case_manager_rv') and obj.case_manager_rv.filter(id=profile.id).exists():
                return True

            # For Dispute model
            from odr.models import Dispute
            if isinstance(obj, Dispute):
                return obj.case_manager_rv.filter(id=profile.id).exists()

            # For dispute-related objects
            if hasattr(obj, 'dispute'):
                return obj.dispute.case_manager_rv.filter(id=profile.id).exists()

            # For campaign-related objects that might be linked to disputes
            if hasattr(obj, 'campaign'):
                # Check if any disputes in this campaign are assigned to this case manager
                campaign_disputes = Dispute.objects.filter(campaign=obj.campaign)
                for dispute in campaign_disputes:
                    if dispute.case_manager_rv.filter(id=profile.id).exists():
                        return True

        # ARBITRATOR: Check if the arbitrator is assigned to the dispute
        elif profile_type == ProfileType.arbitrator.name:
            # Direct arbitrator assignment check
            if hasattr(obj, 'arbitrator_rv') and obj.arbitrator_rv.filter(id=profile.id).exists():
                return True

            # For Dispute model
            from odr.models import Dispute
            if isinstance(obj, Dispute):
                return obj.arbitrator_rv.filter(id=profile.id).exists()

            # For dispute-related objects
            if hasattr(obj, 'dispute'):
                return obj.dispute.arbitrator_rv.filter(id=profile.id).exists()

        # Check if user created the object (applies to all profile types)
        if hasattr(obj, 'created_by') and obj.created_by == user:
            return True

        return False


class IsCaseManager(permissions.BasePermission):
    """
    Permission class specifically for case managers.
    Allows case managers to:
    1. Create cases
    2. Send notices
    3. Assign arbitrators and case managers
    4. Access only cases they're assigned to
    """
    def has_permission(self, request, view):
        user = request.user
        # Check if user is a case manager
        return Profile.objects.filter(user=user, profile_type=ProfileType.case_manager.name).exists()

    def has_object_permission(self, request, view, obj):
        user = request.user

        try:
            profile = Profile.objects.get(user=user, profile_type=ProfileType.case_manager.name)
        except Profile.DoesNotExist:
            return False

        # For Dispute model
        from odr.models import Dispute
        if isinstance(obj, Dispute):
            return obj.case_manager_rv.filter(id=profile.id).exists()

        # For dispute-related objects
        if hasattr(obj, 'dispute'):
            return obj.dispute.case_manager_rv.filter(id=profile.id).exists()

        # For campaign-related objects
        if hasattr(obj, 'campaign'):
            # Check if any disputes in this campaign are assigned to this case manager
            campaign_disputes = Dispute.objects.filter(campaign=obj.campaign)
            for dispute in campaign_disputes:
                if dispute.case_manager_rv.filter(id=profile.id).exists():
                    return True

        # Check if user created the object
        if hasattr(obj, 'created_by') and obj.created_by == user:
            return True

        return False


class IsArbitrator(permissions.BasePermission):
    """
    Permission class specifically for arbitrators.
    Allows arbitrators to access only cases they're assigned to and related notices.
    """
    def has_permission(self, request, view):
        user = request.user
        # Check if user is an arbitrator
        return Profile.objects.filter(user=user, profile_type=ProfileType.arbitrator.name).exists()

    def has_object_permission(self, request, view, obj):
        user = request.user

        try:
            profile = Profile.objects.get(user=user, profile_type=ProfileType.arbitrator.name)
        except Profile.DoesNotExist:
            return False

        # For Dispute model
        from odr.models import Dispute
        if isinstance(obj, Dispute):
            return obj.arbitrator_rv.filter(id=profile.id).exists()

        # For dispute-related objects
        if hasattr(obj, 'dispute'):
            return obj.dispute.arbitrator_rv.filter(id=profile.id).exists()

        # Check if user created the object
        if hasattr(obj, 'created_by') and obj.created_by == user:
            return True

        return False


class IsAnyRole(permissions.BasePermission):
    """
    Permission class that allows access if the user satisfies any of the required roles.
    This is useful for endpoints that should be accessible by multiple user types.
    """
    def has_permission(self, request, view):
        user = request.user

        # Check if user is authenticated
        if not user.is_authenticated:
            return False

        # Check if user is admin
        is_admin = Profile.objects.filter(user=user, profile_type=ProfileType.admin.name).exists()
        if is_admin:
            return True
        is_sub_admin = Profile.objects.filter(user=user, profile_type=ProfileType.sub_admin.name).exists()
        if is_sub_admin:
            return True

        # Check if user is client or sub_client
        is_client = Profile.objects.filter(user=user, profile_type__in=[ProfileType.client.name, ProfileType.sub_client.name]).exists()
        if is_client:
            return True

        # Check if user is case manager
        is_case_manager = Profile.objects.filter(user=user, profile_type=ProfileType.case_manager.name).exists()
        if is_case_manager:
            return True

        # Check if user is arbitrator
        is_arbitrator = Profile.objects.filter(user=user, profile_type=ProfileType.arbitrator.name).exists()
        if is_arbitrator:
            return True

        return False

    def has_object_permission(self, request, view, obj):
        user = request.user

        # Check if user is admin
        is_admin = Profile.objects.filter(user=user, profile_type=ProfileType.admin.name).exists()
        if is_admin:
            return True

        # Try client/sub-client permission
        try:
            profile = Profile.objects.get(user=user, profile_type__in=[ProfileType.client.name, ProfileType.sub_client.name])

            # Direct ownership checks
            if hasattr(obj, 'client') and obj.client == profile:
                return True
            elif hasattr(obj, 'client_id') and obj.client_id == profile.id:
                return True
            elif hasattr(obj, 'profile') and obj.profile == profile:
                return True
            elif hasattr(obj, 'profile_id') and obj.profile_id == profile.id:
                return True

            # For dispute-related objects
            if hasattr(obj, 'dispute'):
                if hasattr(obj.dispute, 'client') and obj.dispute.client == profile:
                    return True
                elif hasattr(obj.dispute, 'client_id') and obj.dispute.client_id == profile.id:
                    return True

            # For campaign-related objects
            if hasattr(obj, 'campaign'):
                if hasattr(obj.campaign, 'client') and obj.campaign.client == profile:
                    return True
                elif hasattr(obj.campaign, 'client_id') and obj.campaign.client_id == profile.id:
                    return True
        except Profile.DoesNotExist:
            pass

        # Try case manager permission
        try:
            profile = Profile.objects.get(user=user, profile_type=ProfileType.case_manager.name)

            # Direct case manager assignment check
            if hasattr(obj, 'case_manager_rv') and obj.case_manager_rv.filter(id=profile.id).exists():
                return True

            # For Dispute model
            from odr.models import Dispute
            if isinstance(obj, Dispute):
                if obj.case_manager_rv.filter(id=profile.id).exists():
                    return True

            # For dispute-related objects
            if hasattr(obj, 'dispute'):
                if obj.dispute.case_manager_rv.filter(id=profile.id).exists():
                    return True

            # For campaign-related objects that might be linked to disputes
            if hasattr(obj, 'campaign'):
                # Check if any disputes in this campaign are assigned to this case manager
                campaign_disputes = Dispute.objects.filter(campaign=obj.campaign)
                for dispute in campaign_disputes:
                    if dispute.case_manager_rv.filter(id=profile.id).exists():
                        return True
        except Profile.DoesNotExist:
            pass

        # Try arbitrator permission
        try:
            profile = Profile.objects.get(user=user, profile_type=ProfileType.arbitrator.name)

            # Direct arbitrator assignment check
            if hasattr(obj, 'arbitrator_rv') and obj.arbitrator_rv.filter(id=profile.id).exists():
                return True

            # For Dispute model
            from odr.models import Dispute
            if isinstance(obj, Dispute):
                if obj.arbitrator_rv.filter(id=profile.id).exists():
                    return True

            # For dispute-related objects
            if hasattr(obj, 'dispute'):
                if obj.dispute.arbitrator_rv.filter(id=profile.id).exists():
                    return True
        except Profile.DoesNotExist:
            pass

        # Check if user created the object (applies to all profile types)
        if hasattr(obj, 'created_by') and obj.created_by == user:
            return True

        return False