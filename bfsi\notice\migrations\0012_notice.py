# Generated by Django 3.2 on 2025-04-16 06:03

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('odr', '0013_dispute_client'),
        ('notice', '0011_campaign_whatsapp_messages_sent'),
    ]

    operations = [
        migrations.CreateModel(
            name='Notice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file_path', models.Char<PERSON>ield(blank=True, max_length=1000, null=True)),
                ('file_name', models.CharField(blank=True, max_length=1000, null=True)),
                ('notice_type', models.CharField(blank=True, max_length=1000, null=True)),
                ('size', models.CharField(blank=True, max_length=1000, null=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, to=settings.AUTH_USER_MODEL)),
                ('dispute', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='odr.dispute')),
            ],
        ),
    ]
