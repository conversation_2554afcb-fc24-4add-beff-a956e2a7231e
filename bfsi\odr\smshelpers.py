# import requests

# from bfsi.settings import (BASE_URI_2FACTOR, OTP_TEMPLATE_NAME,
#                               SMS_ENDPOINT, TWO_FACTOR_API_KEY,
#                               TWO_FACTOR_API_URL,
#                               TWO_FACTOR_TRANSACTIONAL_API_URL)


# def send_otp_sms(otp, mobile=""):
#     try:
#         headers = {"content-type": "application/x-www-form-urlencoded"}
#         payload = ''
#         url = f'{BASE_URI_2FACTOR}{TWO_FACTOR_API_URL}{TWO_FACTOR_API_KEY}/{SMS_ENDPOINT}/{str(mobile)}/{str(otp)}/{OTP_TEMPLATE_NAME}'
#         response = requests.request("GET", url, headers=headers, data=payload)
#         print(response)
#         print(response.content)
#         print(response.json())
#         return response
#     except Exception as e:
#         print(str(e))


# def send_meeting_link_sms(mobile, link, date):
#     try:
#         module = 'TRANS_SMS'
#         template_name = 'meeting'
#         header = 'WENYAY'
#         peid = '1301162571755822382'
#         ctid = '1307168629112086237'
#         payload = f'module={module}&apikey={TWO_FACTOR_API_KEY}&to={mobile}&from={header}&templatename={template_name}&var1={date}&var2={link}&peid={peid}&ctid={ctid}'
#         url = f'{BASE_URI_2FACTOR}{TWO_FACTOR_TRANSACTIONAL_API_URL}'
#         headers = {}
#         response = requests.request("POST", url, headers=headers, data=payload)
#         print(response)
#         print(response.content)
#         print(response.json())
#     except Exception as e:
#         print(str(e))


# def send_meeting_link_john_deere_sms(mobile, loan_account_no):
#     try:
#         module = 'TRANS_SMS'
#         template_name = 'meetinglinkjohndeere'
#         header = 'WENYAY'
#         peid = '1301162571755822382'
#         ctid = '1307170808504666304'
#         payload = f'module={module}&apikey={TWO_FACTOR_API_KEY}&to={mobile}&from={header}&templatename={template_name}&var1={loan_account_no}&peid={peid}&ctid={ctid}'
#         url = f'{BASE_URI_2FACTOR}{TWO_FACTOR_TRANSACTIONAL_API_URL}'
#         headers = {}
#         response = requests.request("POST", url, headers=headers, data=payload)
#         print(response)
#         print(response.content)
#         print(response.json())
#     except Exception as e:
#         print(str(e))


# def send_meeting_link_john_deere_guj_sms(mobile, loan_account_no):
#     try:
#         module = 'TRANS_SMS'
#         template_name = 'meetinglinkjohndeereguj'
#         header = 'WENYAY'
#         peid = '1301162571755822382'
#         ctid = '1307170808507203476'
#         payload = f'module={module}&apikey={TWO_FACTOR_API_KEY}&to={mobile}&from={header}&templatename={template_name}&var1={loan_account_no}&peid={peid}&ctid={ctid}'
#         url = f'{BASE_URI_2FACTOR}{TWO_FACTOR_TRANSACTIONAL_API_URL}'
#         headers = {}
#         response = requests.request("POST", url, headers=headers, data=payload)
#         print(response)
#         print(response.content)
#         print(response.json())
#     except Exception as e:
#         print(str(e))


# def send_sms_hero(mobile, loan_account_no, template_name, ctid):
#     try:
#         module = 'TRANS_SMS'
#         header = 'WENYAY'
#         peid = '1301162571755822382'
#         # link = "https://bit.ly/3AQ170T"
#         link = "https://noticebyhero.tiiny.site/"
#         payload = f'module={module}&apikey={TWO_FACTOR_API_KEY}&to={mobile}&from={header}&templatename={template_name}&var1={loan_account_no}&var2={link}&peid={peid}&ctid={ctid}'
#         url = f'{BASE_URI_2FACTOR}{TWO_FACTOR_TRANSACTIONAL_API_URL}'
#         headers = {}
#         response = requests.request("POST", url, headers=headers, data=payload)
#         print(response)
#         # print(response.content)
#         print(response.json())
#     except Exception as e:
#         print(str(e))
