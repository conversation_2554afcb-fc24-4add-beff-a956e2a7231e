# Generated by Django 3.2 on 2025-04-18 08:28

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('odr', '0015_profile_parent_client'),
    ]

    operations = [
        migrations.AddField(
            model_name='profile',
            name='gst_number',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='profile',
            name='pan_number',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='profile',
            name='profile_type',
            field=models.CharField(choices=[('general', 'general'), ('arbitrator', 'arbitrator'), ('conciliator', 'conciliator'), ('admin', 'admin'), ('sub_admin', 'sub_admin'), ('case_manager', 'case_manager'), ('client', 'client'), ('sub_client', 'sub_client'), ('claimant', 'claimant'), ('manager', 'manager')], default='general', max_length=300),
        ),
    ]
