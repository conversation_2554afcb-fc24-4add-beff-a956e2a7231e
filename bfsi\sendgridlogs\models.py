from django.db import models

from odr.models import Dispute
from notice.models import Template



class SendgridMail(models.Model):
    email = models.CharField(max_length=120)
    x_message_id = models.CharField(max_length=120, unique=True)
    timestamp = models.DateTimeField(null=True, blank=True)
    dispute = models.ForeignKey(
        Dispute, on_delete=models.CASCADE, null=True, blank=True)
    subject = models.CharField(max_length=255, blank=True, null=True)
    is_co_borrower = models.BooleanField(default=False, blank=True, null=True)
    template = models.ForeignKey(
        Template, on_delete=models.CASCADE, null=True, blank=True)

    def __str__(self):
        return f"Email: {self.email}, X-Message-Id: {self.x_message_id}"


class SendgridEventEntry(models.Model):
    EVENT_CHOICES = (
        ('processed', 'Processed'),
        ('dropped', 'Dropped'),
        ('delivered', 'Delivered'),
        ('deferred', 'Deferred'),
        ('bounce', 'Bounce'),
        ('open', 'Open')
    )
    event = models.CharField(max_length=10, choices=EVENT_CHOICES)
    sendgrid_mail = models.ForeignKey(
        SendgridMail, on_delete=models.CASCADE, null=True, blank=True)
    timestamp = models.DateTimeField(null=True, blank=True)
    sg_event_id = models.CharField(
        max_length=120, unique=True, null=True, blank=True, default=None)
    smtp_id = models.CharField(max_length=120, blank=True, null=True)

    reason = models.CharField(max_length=100, null=True, blank=True)

    def __str__(self):
        return f"Event: {self.event}, {self.sendgrid_mail}"
