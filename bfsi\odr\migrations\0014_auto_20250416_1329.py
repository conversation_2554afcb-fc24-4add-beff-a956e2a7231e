# Generated by Django 3.2 on 2025-04-16 07:59

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('odr', '0013_dispute_client'),
    ]

    operations = [
        migrations.AddField(
            model_name='dispute',
            name='arbitrator_assigned_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='dispute',
            name='arbitrator_rv',
            field=models.ManyToManyField(blank=True, related_name='arbitrator_rv', to='odr.Profile'),
        ),
        migrations.AddField(
            model_name='dispute',
            name='case_manager_assigned_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
    ]
