# Generated by Django 3.2 on 2025-06-26 11:46

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('odr', '0026_auto_20250618_1355'),
    ]

    operations = [
        migrations.AddField(
            model_name='dispute',
            name='closed_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='closedby_profile', to='odr.profile'),
        ),
        migrations.AddField(
            model_name='dispute',
            name='closed_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.DeleteModel(
            name='CaseFile',
        ),
    ]
