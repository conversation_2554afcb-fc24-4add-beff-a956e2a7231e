# serializers.py
from rest_framework import serializers
from .models import SendgridMail, SendgridEventEntry

class SendgridMailSerializer(serializers.ModelSerializer):
    class Meta:
        model = SendgridMail
        fields = ['email', 'x_message_id', 'dispute_id', 'subject']

class SendgridEventEntrySerializer(serializers.ModelSerializer):
    # Custom serializer method to extract X-Message-Id from sendgrid_mail field
    x_message_id = serializers.SerializerMethodField()

    class Meta:
        model = SendgridEventEntry
        fields = ['event', 'sendgrid_mail', 'x_message_id', 'timestamp', 'sg_event_id', 'smtp_id', 'reason']

    def get_x_message_id(self, obj):
        # Extract X-Message-Id from the sendgrid_mail field
        sendgrid_mail_field = obj.sendgrid_mail
        x_message_id_start = sendgrid_mail_field.find('X-Message-Id:') + len('X-Message-Id:')
        x_message_id_end = sendgrid_mail_field.find(',', x_message_id_start)  # Assuming the X-Message-Id is followed by a comma
        x_message_id = sendgrid_mail_field[x_message_id_start:x_message_id_end].strip()

        return x_message_id
