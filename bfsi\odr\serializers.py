import string
from django.shortcuts import get_object_or_404
from django.contrib.auth.hashers import make_password
from django.db import transaction
from rest_framework import serializers
from rest_framework.response import Response
from authorization.models import User
from authorization.serializers import UserSerializer
from notice.serializers import TemplateSerializer
from odr.helperfunctions.databasefunctions import is_production_database
from odr.models import Company, Dispute, LegalDocument, Profile, ProfileType
from notice.models import Campaign, CampaignTemplate, EmailTemplate, Template, WhatsAppTemplate
import pandas as pd
from bfsi.settings import db_logger
from .mailhelpers import sendProfileCreatedBFSI
from odr.helperfunctions.databasefunctions import get_database_name
from django_rest_passwordreset.models import (
    ResetPasswordToken, get_password_reset_token_expiry_time)


REGION_MAPPING = {
    'ANDAMAN AND NICOBAR': 'south',
    'ANDHRA PRADESH': 'south',
    'KARNATAKA': 'south',
    'KERALA': 'south',
    'PONDICHERRY(UT)': 'south',
    'TAMIL NADU': 'south',
    'TELANGANA': 'south',
    'ARUNACHAL PRADESH': 'east',
    'ASSAM': 'east',
    'MANIPUR': 'east',
    'ODISHA': 'east',
    'SIKKIM': 'east',
    'TRIPURA': 'east',
    'WEST BENGAL': 'east',
    'CHHATTISGARH': 'west',
    'GOA': 'west',
    'GUJARAT': 'west',
    'MAHARASHTRA': 'west',
    'MADHYA PRADESH': 'west',
    'DADRA AND NAGAR HAVELI': 'west',
    'DAMAN AND DIU': 'west',
    'BIHAR': 'north',
    'DELHI': 'north',
    'HARYANA': 'north',
    'HIMACHAL PRADESH': 'north',
    'JHARKHAND': 'north',
    'PUNJAB': 'north',
    'RAJASTHAN': 'north',
    'UTTAR PRADESH': 'north',
    'UTTARAKHAND': 'north',
    'CHANDIGARH': 'north',
    'JAMMU & KASHMIR': 'north',
    'AHMEDABAD': 'ahmedabad',
}

class LegalDocumentSerializer(serializers.ModelSerializer):
    class Meta:
        model = LegalDocument
        fields = '__all__'


class DisputeSerializer(serializers.ModelSerializer):
    all_docs = LegalDocumentSerializer(many=True, read_only=True)

    class Meta:
        model = Dispute
        fields = '__all__'


class CustomUserSerializer(serializers.ModelSerializer):
    user = UserSerializer(many=False)

    class Meta:
        model = Profile
        fields = '__all__'

    def get_default_templates_data(self):
        """Define the 8 generic templates data for new clients"""
        return [
            # Template 1
            {
                'whatsapp': {
                    'whatsapp_template_id': 'generic_s21_notice',
                    'name': 'Generic | s21 notice',
                    'body': '<p>Dear Sir, We hope this message finds you well. We are writing to inform you that an arbitration proceeding has been initiated regarding the matter between you and client_name. As part of this process, a S21 notice of arbitration has been issued and is attached to this email for your reference. Please take the time to review the attached notice carefully. It provides important information regarding the arbitration process. If you have any questions or concerns, please do not hesitate to contact us directly.</p><p>Regards M&amp;K Law Associates</p>',
                    'lang_code': 'en_US',
                    'requires_attachment': True,
                    'has_body_params': True,
                    'is_s21_notice': True,
                    'is_s138_notice': False
                },
                'email': {
                    'email_template_id': 'd-4df48cf2e3864fc28bc26c9bc520426f',
                    'name': 'Generic | s21 notice',
                    'body': '<p>Dear Sir, We hope this message finds you well. We are writing to inform you that an arbitration proceeding has been initiated regarding the matter between you and client_name. As part of this process, a S21 notice of arbitration has been issued and is attached to this email for your reference. Please take the time to review the attached notice carefully. It provides important information regarding the arbitration process. If you have any questions or concerns, please do not hesitate to contact us directly.</p><p>Regards M&amp;K Law Associates</p>',
                    
                    'requires_attachment': True,
                    # 'has_body_params': True,
                    'is_termination_notice': False,
                    'is_s21_notice': True,
                    'is_s138_notice': False,
                    # 'file_section_template': False
                },
                'parent': {
                    # 'template_id': 't75',
                    'name': 'S21 Notice',
                    'description': 'm&k'
                }
            },
            # Template 2
            {
                'whatsapp': {
                    'whatsapp_template_id': 'generic_statement_of_claim_submitted',
                    'name': 'Generic | Statement of Claim Submitted',
                    'body': '<p>Dear Respondent,</p><p>We are writing to you on behalf of Webnyay to notify you that, as the claimant has submitted the Statement of Claim, we request you to submit the Statement of Defence and Counterclaim (if any) as soon as possible.</p><p>Kindly confirm the date on which you intend to submit the Statement of Defence and Counterclaim (if any), and your availability for the hearing.</p><p>Should any of the parties have any questions, or need further clarification on any aspect of the proceedings, please do not hesitate to reach out to us.</p><p>Regards Team Webnyay</p>',
                    'lang_code': 'en_US',
                    'requires_attachment': True,
                    'has_body_params': False,
                    'is_s21_notice': False,
                    'is_s138_notice': False
                },
                'email': {
                    'email_template_id': 'd-694b2b5daacb460b9370ebd40f98483c',
                    'name': 'Generic | Statement of Claim Submitted',
                    'body': '<p>Dear Respondent,</p><p>We are writing to you on behalf of Webnyay to notify you that, as the claimant has submitted the Statement of Claim, we request you to submit the Statement of Defence and Counterclaim (if any) as soon as possible.</p><p>Kindly confirm the date on which you intend to submit the Statement of Defence and Counterclaim (if any), and your availability for the hearing.</p><p>Should any of the parties have any questions, or need further clarification on any aspect of the proceedings, please do not hesitate to reach out to us.</p><p>Regards Team Webnyay</p>',
                    'requires_attachment': True,
                    # 'has_body_params': False,
                    'is_termination_notice': False,
                    'is_s21_notice': False,
                    'is_s138_notice': False,
                    # 'file_section_template': False
                },
                'parent': {
                    # 'template_id': 't76',
                    'name': 'Statement of Claim Submitted',
                    'description': 'Generic template for arbitrator appointment reminder'
                }
            },
            # Template 3
            {
                'whatsapp': {
                    'whatsapp_template_id': 'generic_reminder_1_submission_of_statement_of_defence_',
                    'name': 'Generic | Reminder 1- Arbitrator Appointment',
                    'body': '<p> Dear parties,</p><p>We are writing to you on behalf of Webnyay to formally notify you that, the claimant has submitted the Statement of Claim, we request you to submit the Statement of Defence and counterclaim (if any) within 7 days. Otherwise, the arbitrator shall proceed ex parte. If proceedings continue ex-parte, then the arbitrator shall issue an award on the basis of the statement of claim and other documents submitted in the proceeding.</p><p>Should any of the parties have any questions, or need further clarification on any aspect of the proceedings, please do not hesitate to reach out to us.</p><p>Regards  Team Webnyay</p>',
                    'lang_code': 'en_US',
                    'requires_attachment': False,
                    'has_body_params': False,
                    'is_s21_notice': False,
                    'is_s138_notice': False
                },
                'email': {
                    'email_template_id': 'd-ae810eaf55094e13ba48890f992c1c85',
                    'name': 'Generic | Reminder 1- Arbitrator Appointment',
                    'body': '<p> Dear parties,</p><p>We are writing to you on behalf of Webnyay to formally notify you that, the claimant has submitted the Statement of Claim, we request you to submit the Statement of Defence and counterclaim (if any) within 7 days. Otherwise, the arbitrator shall proceed ex parte. If proceedings continue ex-parte, then the arbitrator shall issue an award on the basis of the statement of claim and other documents submitted in the proceeding.</p><p>Should any of the parties have any questions, or need further clarification on any aspect of the proceedings, please do not hesitate to reach out to us.</p><p>Regards  Team Webnyay</p>',
                    'requires_attachment': False,
                    # 'has_body_params': False,
                    'is_termination_notice': False,
                    'is_s21_notice': False,
                    'is_s138_notice': False,
                    # 'file_section_template': False
                },
                'parent': {
                    # 'template_id': 't76',
                    'name': 'Reminder 1- Submission of Statement of Defence',
                    'description': 'reminder 1'
                }
            },
            # Template 4
            {
                'whatsapp': {
                    'whatsapp_template_id': 'generic_reminder_2_submission_of_statement_of_defence',
                    'name': 'Generic | Reminder 2- Submission of Statement of Defence',
                    'body': '<p>Dear parties,</p><p>We are writing to you on behalf of Webnyay to formally notify you that, the claimant has submitted the Statement of Claim, we request you to submit the Statement of Defence and counterclaim (if any) within 3 days. Otherwise, the arbitrator shall proceed ex parte. If proceedings continue ex-parte, then the arbitrator shall issue an award on the basis of the statement of claim and other documents submitted in the proceeding.</p><p>Should any of the parties have any questions, or need further clarification on any aspect of the proceedings, please do not hesitate to reach out to us.</p><p>Regards  Team Webnyay</p>',
                    'lang_code': 'en_US',
                    'requires_attachment': False,
                    'has_body_params': False,
                    'is_s21_notice': False,
                    'is_s138_notice': False
                },
                'email': {
                    'email_template_id': 'd-67ed6d5a620f42e8863a831daa4b2bef',
                    'name': 'Generic | Reminder 2- Submission of Statement of Defence',
                    'body': '<p>Dear parties,</p><p>We are writing to you on behalf of Webnyay to formally notify you that, the claimant has submitted the Statement of Claim, we request you to submit the Statement of Defence and counterclaim (if any) within 3 days. Otherwise, the arbitrator shall proceed ex parte. If proceedings continue ex-parte, then the arbitrator shall issue an award on the basis of the statement of claim and other documents submitted in the proceeding.</p><p>Should any of the parties have any questions, or need further clarification on any aspect of the proceedings, please do not hesitate to reach out to us.</p><p>Regards  Team Webnyay</p>',
                    'requires_attachment': False,
                    # 'has_body_params': False,
                    'is_termination_notice': False,
                    'is_s21_notice': False,
                    'is_s138_notice': False,
                    # 'file_section_template': False
                },
                'parent': {
                    # 'template_id': 't76',
                    'name': 'Reminder 2- Submission of Statement of Defence',
                    'description': 'reminder 2'
                }
            },
            # Template 5
            {
                'whatsapp': {
                    'whatsapp_template_id': 'generic_final_award',
                    'name': 'Generic | Final Award',
                    'body': '<p>Dear Parties, We write to inform you that the final arbitral award in your matter administered by the Webnyay ODR Institution has been issued. Please find the final award attached to this email for your reference. A copy of the award is also on the portal in the Files Section. Pursuant to Section 33 of the Arbitration &amp; Conciliation Act, 1996, either party may request the Arbitrator to correct any computational, clerical, typographical, or similar errors in the award. Any such request should be submitted in writing to <NAME_EMAIL> with the relevant Case Manager in CC, with a copy also to the other party. Should you require an attested copy of the final award, you may request the same by sending an email to to <NAME_EMAIL> with the relevant Case Manager in CC. Please note that a fee will be payable for certification, to cover the cost of printing and courier.</p><p>Regards Team Webnyay</p>',
                    'lang_code': 'en_US',
                    'requires_attachment': True,
                    'has_body_params': False,
                    'is_s21_notice': False,
                    'is_s138_notice': False
                },
                'email': {
                    'email_template_id': 'd-a8a78c9595cd4bc6b089a20caf77aab5',
                    'name': 'Generic | Final Award',
                    'body': '<p>Dear Parties, We write to inform you that the final arbitral award in your matter administered by the Webnyay ODR Institution has been issued. Please find the final award attached to this email for your reference. A copy of the award is also on the portal in the Files Section. Pursuant to Section 33 of the Arbitration &amp; Conciliation Act, 1996, either party may request the Arbitrator to correct any computational, clerical, typographical, or similar errors in the award. Any such request should be submitted in writing to <NAME_EMAIL> with the relevant Case Manager in CC, with a copy also to the other party. Should you require an attested copy of the final award, you may request the same by sending an email to to <NAME_EMAIL> with the relevant Case Manager in CC. Please note that a fee will be payable for certification, to cover the cost of printing and courier.</p><p>Regards Team Webnyay</p>',
                    'requires_attachment': True,
                    # 'has_body_params': False,
                    'is_termination_notice': False,
                    'is_s21_notice': False,
                    'is_s138_notice': False,
                    # 'file_section_template': False
                },
                'parent': {
                    # 'template_id': 't76',
                    'name': 'Final Award',
                    'description': 'Final Award'
                }
            },
            # Template 6
            {
                'whatsapp': {
                    'whatsapp_template_id': 'generic_commencement_of_arbitration_notice',
                    'name': 'Generic | Commencement of Arbitration Notice',
                    'body': '<p>Dear Sir, We hope this message finds you well. We are writing to inform you that an arbitration proceeding has been initiated regarding the matter between you and client_name. As part of this process, a formal notice of arbitration has been issued and is attached to this email for your reference. Please take the time to review the attached notice carefully. It provides important information regarding the arbitration process. If you have any questions or concerns, please do not hesitate to contact us directly.</p><p>Regards Team Webnyay</p>',
                    'lang_code': 'en_US',
                    'requires_attachment': True,
                    'has_body_params': True,
                    'is_s21_notice': False,
                    'is_s138_notice': False
                },
                'email': {
                    'email_template_id': 'd-23b10f2ddc424532858fcb9f0f61dd58',
                    'name': 'Generic | Commencement of Arbitration Notice',
                    'body': '<p>Dear Sir, We hope this message finds you well. We are writing to inform you that an arbitration proceeding has been initiated regarding the matter between you and client_name. As part of this process, a formal notice of arbitration has been issued and is attached to this email for your reference. Please take the time to review the attached notice carefully. It provides important information regarding the arbitration process. If you have any questions or concerns, please do not hesitate to contact us directly.</p><p>Regards Team Webnyay</p>',
                    'requires_attachment': True,
                    # 'has_body_params': True,
                    'is_termination_notice': False,
                    'is_s21_notice': False,
                    'is_s138_notice': False,
                    # 'file_section_template': False
                },
                'parent': {
                    # 'template_id': 't76',
                    'name': 'Commencement of Arbitration Notice',
                    'description': 'Commencement of Arbitration Notice'
                }
            },
            # Template 7
            {
                'whatsapp': {
                    'whatsapp_template_id': 'generic_interim_relief_application_submitted',
                    'name': 'Generic | Interim Relief Application Submitted',
                    'body': '<p>Dear Sir/Madam,</p><p>client_name has submitted an application seeking interim relief from the Arbitrator in the form of an interim award. The Claimant&rsquo;s application can be downloaded from the Files Section of the Webnyay Portal. The Arbitrator may call for a hearing to decide the application (if it deems fit).</p><p>Should any of the parties have any questions, or need further clarification on any aspect of the proceedings, please do not hesitate to reach out to us.</p><p>Regards Team Webnyay</p>',
                    'lang_code': 'en_US',
                    'requires_attachment': True,
                    'has_body_params': True,
                    'is_s21_notice': False,
                    'is_s138_notice': False
                },
                'email': {
                    'email_template_id': 'd-3b45e7c7a0fa4bf9a1c0cb840d72506d',
                    'name': 'Generic | Interim Relief Application Submitted',
                    'body': '<p>Dear Sir/Madam,</p><p>client_name has submitted an application seeking interim relief from the Arbitrator in the form of an interim award. The Claimant&rsquo;s application can be downloaded from the Files Section of the Webnyay Portal. The Arbitrator may call for a hearing to decide the application (if it deems fit).</p><p>Should any of the parties have any questions, or need further clarification on any aspect of the proceedings, please do not hesitate to reach out to us.</p><p>Regards Team Webnyay</p>',
                    'requires_attachment': True,
                    # 'has_body_params': True,
                    'is_termination_notice': False,
                    'is_s21_notice': False,
                    'is_s138_notice': False,
                    # 'file_section_template': False
                },
                'parent': {
                    # 'template_id': 't76',
                    'name': 'Interim Relief Application Submitted',
                    'description': 'Interim Relief Application Submitted'
                }
            },
            # Template 8
            {
                'whatsapp': {
                    'whatsapp_template_id': 'file_section_template',
                    'name': 'File section template',
                    'body': '<p>Dear Sir/Madam,</p><p>This is to inform you that documents have been uploaded to your arbitration case with client_name. Important: You have the right to review these documents and respond within 3 days as per the arbitration rules.</p><p>Failure to respond may affect your case.</p><p>Regards Team Webnyay</p>',
                    'lang_code': 'en_US',
                    'requires_attachment': True,
                    'has_body_params': True,
                    'is_s21_notice': False,
                    'is_s138_notice': False
                    
                },
                'email': {
                    'email_template_id': 'd-c43cc930308443db8fce13f2619ca589',
                    'name': 'File section template',
                    'body': '<p>Dear Sir/Madam,</p><p>This is to inform you that documents have been uploaded to your arbitration case with client_name. Important: You have the right to review these documents and respond within 3 days as per the arbitration rules.</p><p>Failure to respond may affect your case.</p><p>Regards Team Webnyay</p>',
                    'requires_attachment': True,
                    # 'has_body_params': True,
                    'is_termination_notice': False,
                    'is_s21_notice': False,
                    'is_s138_notice': False,
                    # 'file_section_template': True
                },
                'parent': {
                    # 'template_id': 't76',
                    'name': 'File section template',
                    'description': 'file_section_template',
                    'file_section_template': True
                }
            },
            # Template 9
            {
                'whatsapp': {
                    'whatsapp_template_id': 'file_section_template_mk',
                    'name': 'File section template mk',
                    'body': '<p>Dear Sir/Madam,</p><p>This is to inform you that documents have been uploaded to your arbitration case with client_name. Important: You have the right to review these documents and respond within 3 days as per the arbitration rules.</p><p>Failure to respond may affect your case.</p><p>Regards M&amp;K Law Associates</p>',
                    'lang_code': 'en_US',
                    'requires_attachment': True,
                    'has_body_params': True,
                    'is_s21_notice': True,
                    'is_s138_notice': False
                },
                'email': {
                    'email_template_id': 'd-4f466d3766d3417b965677f35ddda9c0',
                    'name': 'File section template mk',
                    'body': '<p>Dear Sir/Madam,</p><p>This is to inform you that documents have been uploaded to your arbitration case with client_name. Important: You have the right to review these documents and respond within 3 days as per the arbitration rules.</p><p>Failure to respond may affect your case.</p><p>Regards M&amp;K Law Associates</p>',
                    'requires_attachment': True,
                    # 'has_body_params': True,
                    'is_termination_notice': False,
                    'is_s21_notice': True,
                    'is_s138_notice': False,
                    # 'file_section_template': True
                },
                'parent': {
                    # 'template_id': 't76',
                    'name': 'File section template mk',
                    'description': 'file section template mk',
                    'file_section_template': True
                }
            },
            #Template 10
            {
                'whatsapp': {
                    'whatsapp_template_id': 'generic_conciliation_template_1',
                    'name': 'Generic Conciliation template 1',
                    'body': '<p>Dear User</p><p>Conciliation proceedings for your Loan account number [loan_id_test] obtained through [client_name] will be held online. Date: [Date} Time: {from_time} to {end_time}</p><p>Kindly refer to the attached notice for details.</p><p>Join&#160;hearing&#160;online</p>',
                    'lang_code': 'en_US',
                    'requires_attachment': True,
                    'has_body_params': True,
                    'is_s21_notice':False ,
                    'is_s138_notice': False,
                    'conciliation_notice_1': True
                },
                'email': {
                    'email_template_id': 'd-2574a3bd27ff4492b9c4e97208dd5315',
                    'name': 'Generic Conciliation template 1',
                    'body': '<p>Dear User</p><p>We are writing to inform you of the initiation of Conciliation proceedings concerning an outstanding sum of money owed by you.</p><p>We have been approached by {client_name} in association with the loan obtained by you to commence Conciliation proceedings so the Company can reach an agreement with you in respect of certain sums of money that you owe to the Company.</p><p>For your benefit, please note that Conciliation is a legal process where a neutral third party, called a Conciliator (who will be appointed by Webnyay Pvt Ltd (the Institution), will help to settle the case between you and the Company. You, the authorized representative of the Company and the Conciliator will meet online, discuss the problem and your circumstances, and try to find a fair solution. It\'s important to participate in the conciliation proceedings because it\'s faster, less expensive than commencing formal court proceedings. The decision reached in conciliation proceedings will be legally binding.</p><p>You are hereby served notice to attend the Conciliation Proceedings. Please participate in the online session on {date} starting from {start_time} to {end_time}.</p><p>For further information and details about the Conciliation, please contact {case_manager_name} from Webnyay at {case_manager_emailID}.</p><p>We urge you to consider this seriously and participate actively in the Conciliation process.   Best regards, Webnyay Private Limited</p><p>Button: Join&#160;hearing&#160;online</p>',
                    'requires_attachment': True,
                    # 'has_body_params': True,
                    'is_termination_notice': False,
                    'is_s21_notice': False,
                    'is_s138_notice': False,
                    'conciliation_notice_1': True
                    # 'file_section_template': True
                },
                'parent': {
                    # 'template_id': 't76',
                    'name': 'Conciliation template 1',
                    'description': 'Invitation to Participate in Conciliation Proceedings',
                    'file_section_template': False
                }
            },
            # Template 11
            {
                'whatsapp': {
                    'whatsapp_template_id': 'generic_conciliation_template_2',
                    'name': 'Generic Conciliation template 2',
                    'body': '<p>&#55357;&#56596;Conciliation hearing coming up soon!</p><p>Dear User, this is your reminder to participate in the upcoming Conciliation proceedings for your loan account with {client_name}. Join us on {date} between {start_time} and {end_time}.</p><p>Button: Join&#160;hearing&#160;online</p>',
                    'lang_code': 'en',
                    'requires_attachment': False,
                    'has_body_params': True,
                    'is_s21_notice': False,
                    'is_s138_notice': False,
                    'conciliation_notice_2': True
                },
                'parent': {
                    # 'template_id': 't76',
                    'name': 'Conciliation template 2',
                    'description': 'generic_conciliation_template_2',
                    'file_section_template': False
                }
            },
            # Template 12
            {
                'whatsapp': {
                    'whatsapp_template_id': 'generic_conciliation_template_3',
                    'name': 'Conciliation template 3',
                    'body': '<p>&#9200; Final Opportunity for Resolution - Join the Conciliation hearing on {date}!</p><p>Dear User, this is your final opportunity to participate in Conciliation proceedings for your loan account with {client_name}. Join us on {date} between {start_time} and {end_time}.</p><p>Button: Join conciliation&#160;hearing</p>',
                    'lang_code': 'en_US',
                    'requires_attachment': False,
                    'has_body_params': True,
                    'is_s21_notice': False,
                    'is_s138_notice': False,
                    'conciliation_notice_3': True
                },
                'parent': {
                    # 'template_id': 't76',
                    'name': 'Conciliation template 3',
                    'description': 'generic conciliation template 3',
                    'file_section_template': False
                }
            },
            # Template 13
            {
                'whatsapp': {
                    'whatsapp_template_id': 'generic_conciliation_template_4',
                    'name': 'Generic Conciliation template 4',
                    'body': '<p>Dear User, Conciliation proceedings for your loan with {client_name} have now started. Kindly attend the proceedings without fail.</p><p> Join&#160;hearing&#160;online:link</p>',
                    'lang_code': 'en_US',
                    'requires_attachment': False,
                    'has_body_params': True,
                    'is_s21_notice': False,
                    'is_s138_notice': False,
                    'conciliation_notice_4': True
                },
                'email': {
                    'email_template_id': 'd-539ef5be3cd14f19b211b8d26b863cda',
                    'name': 'Generic Conciliation template 4',
                    'body': '<p>Dear User,</p><p>This pertains to the prior Notice/Email dated {date} sent to you earlier, serving as a gentle reminder to review the contents of the previous email if not already done so.</p><p>The Conciliation Proceedings for your aforementioned loan facility have started. We urge you to consider this seriously and participate actively in the Conciliation process.</p><p>Best regards, Webnyay Private Limited</p><p> Join&#160;hearing&#160;online : link</p>',
                    'requires_attachment': False,
                    # 'has_body_params': True,
                    'is_termination_notice': False,
                    'is_s21_notice': False,
                    'is_s138_notice': False,
                    'conciliation_notice_4': True
                    # 'file_section_template': True
                },
                'parent': {
                    # 'template_id': 't76',
                    'name': 'Conciliation template 4',
                    'description': 'generic conciliation template 4',
                    'file_section_template': False
                }
            },

            # Template 14
            {
                'email': {
                    'email_template_id': 'd-5b8b39c54bc44b7ea2a50a7d4d37a476',
                    'name': 'Reminder for Conciliation camp',
                    'body': '<p>Final Opportunity for Resolution - Join the Conciliation hearing on {date}!</p><p>Dear User, this is your final opportunity to participate in Conciliation proceedings for your loan account with {client_name}. Join us on {date} between {start_time} and {end_time}.</p><p>Join conciliation&#160;hearing : {{link}}</p>',
                    'requires_attachment': False,
                    # 'has_body_params': True,
                    'is_termination_notice': False,
                    'is_s21_notice': False,
                    'is_s138_notice': False,
                    'conciliation_notice_4': False
                    # 'file_section_template': True
                },
                'parent': {
                    # 'template_id': 't76',
                    'name': 'Email Reminder Conciliation camp',
                    'description': 'Reminder for Conciliation camp',
                    'file_section_template': False
                }
            }
        ]

    def create_generic_templates(self, client_profile):
        """Create generic templates for the client"""
        templates_data = self.get_default_templates_data()
        created_templates = []

        try:
            for i, template_data in enumerate(templates_data):
                whatsapp_template = None
                email_template = None

                # Create WhatsApp template if data exists
                if 'whatsapp' in template_data:
                    whatsapp_data = template_data['whatsapp'].copy()
                    # whatsapp_data['profile'] = client_profile
                    whatsapp_template = WhatsAppTemplate.objects.create(**whatsapp_data)

                # Create Email template if data exists
                if 'email' in template_data:
                    email_data = template_data['email'].copy()
                    # email_data['profile'] = client_profile
                    email_template = EmailTemplate.objects.create(**email_data)

                # Ensure at least one template type exists
                if not whatsapp_template and not email_template:
                    db_logger.warning(f'Template {i+1} has neither WhatsApp nor Email data, skipping...')
                    continue

                # Create Parent template linking available templates
                parent_data = template_data['parent'].copy()
                parent_template = Template.objects.create(
                    template_id=f't{client_profile.id}_gen_{i+1}',
                    name=parent_data['name'],
                    description=parent_data.get('description', ''),
                    whatsapp_template=whatsapp_template,
                    email_template=email_template,
                    profile=client_profile,
                    file_section_template=parent_data.get('file_section_template', False)
                )

                created_templates.append({
                    'whatsapp': whatsapp_template,
                    'email': email_template,
                    'parent': parent_template
                })

        except Exception as e:
            # Log the error and optionally rollback created templates
            db_logger.exception(f'Error creating generic templates for client {client_profile.id}: {str(e)}')
            # Optionally delete already created templates to maintain consistency
            self.cleanup_partial_templates(created_templates)
            raise serializers.ValidationError({
                'templates': f'Failed to create generic templates: {str(e)}'
            })

        return created_templates

    def cleanup_partial_templates(self, created_templates):
        """Clean up partially created templates in case of failure"""
        for template_set in created_templates:
            try:
                if template_set.get('parent'):
                    template_set['parent'].delete()
                if template_set.get('whatsapp'):
                    template_set['whatsapp'].delete()
                if template_set.get('email'):
                    template_set['email'].delete()
            except Exception:
                pass  # Ignore cleanup errors

    def create(self, validated_data):
        user = self.context['request'].user
        profile = get_object_or_404(Profile, user=user)
        profile_type = self.initial_data.get('profile_type')
        if profile.profile_type not in [
            ProfileType.admin.name,
            ProfileType.sub_admin.name,
            ProfileType.case_manager.name,
        ]:
            error = {"data": {"error": f"You are not authorized to create {validated_data.get('profile_type')}."}, "status": 400}
            raise serializers.ValidationError(error)
        company, dispute, company_product = None, None, None

        # Handle claimant/sub_client logic
        if profile_type == "claimant" or profile_type == "sub_client":
            parent_client = self.initial_data.get('client')
            if not parent_client:
                raise serializers.ValidationError({
                    "client": "This field is required for a claimant profile."
                })
            if not Profile.objects.filter(user__id=parent_client).exists():
                raise serializers.ValidationError({
                    "client": "Invalid or non-client parent profile."
                })
            validated_data["parent_client"] = Profile.objects.get(user__id=parent_client)

        # Handle company and dispute validation
        if 'company_id' in self.initial_data.keys():
            company_id = self.initial_data['company_id']
            company = Company.objects.filter(id=company_id).first()
            if not company:
                error = {"data": {"error": "Please provide the valid company id"}, "status": 400}
                raise serializers.ValidationError(error)

        if 'dispute_id' in self.initial_data.keys():
            dispute_id = self.initial_data['dispute_id']
            dispute = Dispute.objects.filter(id=dispute_id).first()
            if not dispute:
                error = {"data": {"error": "Please provide the valid dispute id"}, "status": 400}
                raise serializers.ValidationError(error)

        user_validated_data = validated_data.pop('user')
        characters = string.ascii_letters + string.digits

        if is_production_database():
            password = User.objects.make_random_password(8, characters)
        else:
            password = user_validated_data['password']

        user_validated_data['password'] = make_password(password)
        user_serializer = UserSerializer(data=user_validated_data)

        with transaction.atomic():
            try:
                if user_serializer.is_valid():
                    user = user_serializer.save()
                    user.save()
                    user.set_password(password)
                    user.save(update_fields=['password'])
                    validated_data['user'] = user

                    if profile.profile_type in [
                        ProfileType.admin.name,
                        ProfileType.sub_admin.name,
                        ProfileType.case_manager.name,
                    ]:
                        validated_data['created_by'] = profile.user

                    if company:
                        validated_data['company'] = company
                    if company_product:
                        validated_data['company'] = company_product.company

                    validated_data['created_by_process'] = 'CustomUserSerializer'

                    new_profile = Profile.objects.create(**validated_data)
                    new_profile.created_by_process = 'CustomUserSerializer'
                    new_profile.save()

                    # Handle dispute relationships
                    if dispute and (new_profile.profile_type == ProfileType.arbitrator.name or new_profile.profile_type == ProfileType.conciliator.name):
                        dispute.professionals.add(new_profile)
                        dispute.save()
                    if dispute and new_profile.profile_type == ProfileType.case_manager.name:
                        dispute.case_manager_rv.add(new_profile)
                        dispute.save()

                    # Create generic templates for client profiles
                    if profile_type == ProfileType.client.name:
                        try:
                            created_templates = self.create_generic_templates(new_profile)
                            db_logger.info(f'Successfully created {len(created_templates)} generic templates for client {new_profile.id}')
                        except Exception as template_error:
                            # Log the error but don't fail the entire user creation
                            db_logger.error(f'Failed to create templates for client {new_profile.id}: {str(template_error)}')
                            # You can choose to raise the error or continue without templates
                            # raise template_error  # Uncomment if you want template creation to be mandatory

                    user.temp_password_email_sent = True
                    user.save()

                    # Handle frontend URL and email sending
                    if get_database_name() == 'backend-bfsi-dev':
                        frontend = "https://bfsi-dev-app.webnyay.in/"
                    elif get_database_name() == "backend-bfsi-prod":
                        frontend = "https://bfsi-app.webnyay.in/"

                    token = ResetPasswordToken.objects.create(user_id=user.id)
                    token_url = f'{frontend}reset-password?token={token.key}&email={user.email}'
                    sendProfileCreatedBFSI(email=user.email, password=password, link=frontend, forget_password_link=token_url)
                    
                    return new_profile
                else:
                    raise Response(user_serializer.errors, status=400)
            except Exception as e:
                db_logger.exception('Exception in CustomSerializer ' + str(e))
                error = {'message': 'There is some error'}
                raise serializers.ValidationError(error)


class ClientSerializer(serializers.ModelSerializer):
    name = serializers.SerializerMethodField()
    class Meta:
        model = Profile
        fields = ['id', 'name']
    
    def get_name(self, obj):
        return f"{obj.user.first_name} {obj.user.last_name}".strip() or obj.user.username


# class CampaignSerializer(serializers.ModelSerializer):
#     # created_by = UserSerializer(read_only=True)
#     client = ClientSerializer(read_only=True)
#     templates = serializers.SerializerMethodField()
#     templates_count = serializers.SerializerMethodField()
#     total_cases = serializers.SerializerMethodField()

#     class Meta:
#         model = Campaign
#         # fields = ['id', 'excel_file_name', 'excel_file_uploaded', 'created_at', 
#         #          'case_creation_status', 'total_cases', 'processed_rows', 
#         #          'number_of_cases_created', 'case_managers_assigned', 'assigned_case_managers_at',
#         #          'arbitrators_assigned', 'assigned_arbitrators_at', 'templates', 
#         #          'templates_count', 'client', 'created_by']
#         fields = '__all__'

#     def get_templates(self, obj):
#         templates = Template.objects.filter(profile=obj.client)
#         return TemplateSerializer(templates, many=True).data

#     def get_templates_count(self, obj):
#         return Template.objects.filter(profile=obj.client).count()

#     def get_total_cases(self, obj):
#         return obj.total_rows
    
#     def get_progress(self, obj):
#         return obj.get_progress()


class CustomTemplateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Template
        fields = ['id', 'name', 'template_id', 'whatsapp_template', 'email_template', 'renaming_and_splitting_done']

class CampaignSerializer(serializers.ModelSerializer):
    created_by = UserSerializer(read_only=True)
    templates = serializers.SerializerMethodField()
    progress = serializers.SerializerMethodField()
    client_name = serializers.SerializerMethodField()
    excel_display_name = serializers.SerializerMethodField()

    class Meta:
        model = Campaign
        fields = '__all__'

    def get_progress(self, obj):
        return obj.get_progress()

    def get_templates(self, obj):
        # Get templates associated with this campaign through CampaignTemplate
        # Filter only templates where renaming_and_splitting_done is True
        campaign_templates = CampaignTemplate.objects.filter(
            campaign=obj,
            template__renaming_and_splitting_done=True
        ).select_related('template')
        templates = [ct.template for ct in campaign_templates]
        return CustomTemplateSerializer(templates, many=True).data
    
    def get_client_name(self,obj):        
        if obj.client and obj.client.user: #this check was required as some profiles or users were not associated in DB data.
            return f"{obj.client.user.first_name} {obj.client.user.last_name}"        
        else:
            return ""
    
    
    def get_excel_display_name(self,obj):
        if obj.excel_file_name:
            return obj.excel_file_name.split('/')[-1]
        return ""
    
class CaseFileListSerializer(serializers.ModelSerializer):
    client_name = serializers.CharField(source='client.user.first_name', read_only=True)
    claimant_name = serializers.SerializerMethodField()
    respondent = serializers.SerializerMethodField()
    arbitrator_name = serializers.SerializerMethodField()
    campaign_name = serializers.SerializerMethodField()
    casemanagers = serializers.SerializerMethodField()

    class Meta:
        model = Dispute
        fields = ['id','client','client_name','claimant','claimant_name','respondent','arbitrator_name','created','name','loan_id','status','campaign_name','casemanagers']

    def get_claimant_name(self, obj):        
        claimant = obj.claimant
        if claimant and claimant.user:
            first_name = claimant.user.first_name or ""
            last_name = claimant.user.last_name or ""
            return f"{first_name} {last_name}".strip()
        return None    

    def get_respondent(self,obj):
        return obj.respondents_name
    
    def get_notice_ids(self, obj):
        return list(
            obj.notice_set.values_list('template__template_id', flat=True).distinct()        )

    def get_arbitrator_name(self, obj):
        arbitrator = obj.arbitrator_rv.first()
        if arbitrator and arbitrator.user:
            first_name = arbitrator.user.first_name or ""
            last_name = arbitrator.user.last_name or ""
            return f"{first_name} {last_name}".strip()
        return None

    def get_campaign_name(self,obj):
        if obj.campaign:
            return obj.campaign.excel_file_name
        else:
            return ""
        
    def get_casemanagers(self,obj: Dispute):
        cms = obj.case_manager_rv.all()
        cmlist = []
        for cm in cms:
            cm_dict = dict()
            cm_dict['profile_id'] = cm.id
            profile = get_object_or_404(Profile,id=cm.id)
            cm_dict['user_id'] = profile.user.id
            cm_dict['cm_name'] = f"{profile.user.first_name} {profile.user.last_name}"
            cmlist.append(cm_dict)
        return cmlist

class UserInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['first_name', 'last_name', 'email']

class ProfileMajorDetailSerializer(serializers.ModelSerializer):
    user = UserInfoSerializer()

    class Meta:
        model = Profile
        fields = [
            'user', 'phone_number', 'profile_type', 'id', 'region'
        ]


class ProfileInfoSerializer(serializers.ModelSerializer):
    name = serializers.SerializerMethodField()
    email = serializers.SerializerMethodField()
    phone = serializers.CharField(source='phone_number')
    pincode = serializers.CharField(source='postal_code')

    class Meta:
        model = Profile
        fields = ['name', 'email', 'phone', 'region', 'city', 'state', 'pincode']

    def get_name(self, obj):
        user = obj.user
        return f"{user.first_name} {user.last_name}".strip()

    def get_email(self, obj):
        return obj.user.email
    

class ProfileSerializer(serializers.ModelSerializer):
    user = UserSerializer(many=False, read_only=True)

    class Meta:
        model = Profile
        fields = '__all__'