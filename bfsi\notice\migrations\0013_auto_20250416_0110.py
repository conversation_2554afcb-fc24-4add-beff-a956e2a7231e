# Generated by Django 3.2 on 2025-04-15 19:40

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('odr', '0013_dispute_client'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('notice', '0012_auto_20250416_0103'),
    ]

    operations = [
        migrations.AlterField(
            model_name='campaign',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.RESTRICT, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='whatsapptemplate',
            name='campaign',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, to='notice.campaign'),
        ),
        migrations.AlterField(
            model_name='whatsapptemplate',
            name='profile',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, to='odr.profile'),
        ),
    ]
