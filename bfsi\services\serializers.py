"""
Serializers for SMS models.
Follows the same patterns as existing serializers.
"""

from rest_framework import serializers
from .models import SMSTemplate, SMSMessage, SMSDeliveryReport, SMSCampaign


class SMSTemplateSerializer(serializers.ModelSerializer):
    """Serializer for SMS Template model."""
    
    created_by_email = serializers.CharField(source='created_by.email', read_only=True)
    success_rate = serializers.SerializerMethodField()
    
    class Meta:
        model = SMSTemplate
        fields = [
            'id',
            'sms_template_id',
            'name',
            'body',
            'sender_id',
            'requires_attachment',
            'is_active',
            'sent_to_sms',
            'sms_processed_rows',
            'sms_messages_sent',
            'sms_status',
            'sms_errors',
            'is_s21_notice',
            'is_s138_notice',
            'parameter_mapping',
            'is_termination_notice',
            'is_payment_request_notice',
            'has_body_params',
            'conciliation_notice_1',
            'conciliation_notice_2',
            'conciliation_notice_3',
            'conciliation_notice_4',
            'created_at',
            'updated_at',
            'created_by',
            'created_by_email',
            'success_rate'
        ]
        read_only_fields = [
            'id',
            'sent_to_sms',
            'sms_processed_rows',
            'sms_messages_sent',
            'sms_status',
            'sms_errors',
            'created_at',
            'updated_at',
            'created_by_email',
            'success_rate'
        ]
    
    def get_success_rate(self, obj):
        """Calculate success rate for the template."""
        if obj.sms_messages_sent == 0:
            return 0
        
        # Count successful messages for this template
        successful_messages = SMSMessage.objects.filter(
            sms_template=obj,
            status__in=['DELIVERED', 'ACCEPTED']
        ).count()
        
        return (successful_messages / obj.sms_messages_sent) * 100 if obj.sms_messages_sent > 0 else 0


class SMSMessageSerializer(serializers.ModelSerializer):
    """Serializer for SMS Message model."""
    
    dispute_id = serializers.CharField(source='dispute.id', read_only=True)
    template_name = serializers.CharField(source='template.name', read_only=True)
    sms_template_name = serializers.CharField(source='sms_template.name', read_only=True)
    
    class Meta:
        model = SMSMessage
        fields = [
            'id',
            'phone_number',
            'message_id',
            'message_text',
            'sender_id',
            'status',
            'status_description',
            'sent_timestamp',
            'delivered_timestamp',
            'failed_timestamp',
            'dispute',
            'dispute_id',
            'template',
            'template_name',
            'sms_template',
            'sms_template_name',
            'is_co_borrower',
            'api_response',
            'created_at',
            'updated_at'
        ]
        read_only_fields = [
            'id',
            'message_id',
            'status',
            'status_description',
            'sent_timestamp',
            'delivered_timestamp',
            'failed_timestamp',
            'api_response',
            'created_at',
            'updated_at',
            'dispute_id',
            'template_name',
            'sms_template_name'
        ]


class SMSDeliveryReportSerializer(serializers.ModelSerializer):
    """Serializer for SMS Delivery Report model."""
    
    sms_message_id = serializers.CharField(source='sms_message.id', read_only=True)
    
    class Meta:
        model = SMSDeliveryReport
        fields = [
            'id',
            'message_id',
            'phone_number',
            'status',
            'status_description',
            'error_code',
            'error_description',
            'sent_at',
            'done_at',
            'price_amount',
            'price_currency',
            'webhook_data',
            'sms_message',
            'sms_message_id',
            'created_at',
            'updated_at'
        ]
        read_only_fields = [
            'id',
            'created_at',
            'updated_at',
            'sms_message_id'
        ]


class SMSCampaignSerializer(serializers.ModelSerializer):
    """Serializer for SMS Campaign model."""
    
    sms_template_name = serializers.CharField(source='sms_template.name', read_only=True)
    created_by_email = serializers.CharField(source='created_by.email', read_only=True)
    success_rate = serializers.SerializerMethodField()
    
    class Meta:
        model = SMSCampaign
        fields = [
            'id',
            'name',
            'description',
            'sms_template',
            'sms_template_name',
            'sender_id',
            'status',
            'total_recipients',
            'messages_sent',
            'messages_delivered',
            'messages_failed',
            'scheduled_at',
            'started_at',
            'completed_at',
            'errors',
            'created_by',
            'created_by_email',
            'created_at',
            'updated_at',
            'success_rate'
        ]
        read_only_fields = [
            'id',
            'messages_sent',
            'messages_delivered',
            'messages_failed',
            'started_at',
            'completed_at',
            'created_at',
            'updated_at',
            'sms_template_name',
            'created_by_email',
            'success_rate'
        ]
    
    def get_success_rate(self, obj):
        """Calculate campaign success rate."""
        return obj.get_success_rate()


class SMSTemplateCreateSerializer(serializers.ModelSerializer):
    """Simplified serializer for creating SMS templates."""
    
    class Meta:
        model = SMSTemplate
        fields = [
            'name',
            'body',
            'sender_id',
            'requires_attachment',
            'is_active',
            'is_s21_notice',
            'is_s138_notice',
            'parameter_mapping',
            'is_termination_notice',
            'is_payment_request_notice',
            'has_body_params'
        ]


class SMSMessageCreateSerializer(serializers.ModelSerializer):
    """Simplified serializer for creating SMS messages."""
    
    class Meta:
        model = SMSMessage
        fields = [
            'phone_number',
            'message_text',
            'sender_id',
            'dispute',
            'template',
            'sms_template',
            'is_co_borrower'
        ]


class SMSCampaignCreateSerializer(serializers.ModelSerializer):
    """Simplified serializer for creating SMS campaigns."""
    
    class Meta:
        model = SMSCampaign
        fields = [
            'name',
            'description',
            'sms_template',
            'sender_id',
            'total_recipients',
            'scheduled_at'
        ]


class SMSStatsSerializer(serializers.Serializer):
    """Serializer for SMS statistics."""
    
    total_messages = serializers.IntegerField()
    messages_sent = serializers.IntegerField()
    messages_delivered = serializers.IntegerField()
    messages_failed = serializers.IntegerField()
    success_rate = serializers.FloatField()
    total_templates = serializers.IntegerField()
    active_templates = serializers.IntegerField()
    total_campaigns = serializers.IntegerField()
    active_campaigns = serializers.IntegerField()


class SMSWebhookSerializer(serializers.Serializer):
    """Serializer for processing Infobip webhooks."""
    
    results = serializers.ListField(
        child=serializers.DictField(),
        required=True
    )
    
    def validate_results(self, value):
        """Validate webhook results structure."""
        required_fields = ['messageId', 'to', 'status']
        
        for result in value:
            for field in required_fields:
                if field not in result:
                    raise serializers.ValidationError(
                        f"Missing required field '{field}' in webhook result"
                    )
        
        return value
