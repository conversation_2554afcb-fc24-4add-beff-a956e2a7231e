import re
from django.contrib.auth.hashers import make_password
from django.contrib.auth.password_validation import validate_password
from django.contrib.auth.hashers import make_password
from django.utils import timezone
from rest_framework import serializers

from authorization.models import EmailOtp, User
from odr.models import Profile
from odr.utils import getPlatformLinkByUrl
from django_rest_passwordreset.models import get_password_reset_token_expiry_time
from django.shortcuts import get_object_or_404 as _get_object_or_404
from django.core.exceptions import ValidationError
from django.http import Http404
from . import models
from datetime import timedelta
from django.utils.translation import gettext_lazy as _
from django_rest_passwordreset.models import (
    ResetPasswordToken, get_password_reset_token_expiry_time)





class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ('username', 'email', 'password', 'first_name', 'last_name', 'last_login', 'date_joined', 'is_active', 'temp_password_email_sent',
                  'is_staff', 'id')
        read_only_fields = ('is_active', 'is_staff', 'date_joined', 'id')
        extra_kwargs = {'password': {'write_only': True}}

    def create(self, validated_data):
        validated_data['password'] = make_password(
            validated_data.get('password'))
        return super(UserSerializer, self).create(validated_data)

    def update(self, validated_data):
        validated_data['password'] = make_password(
            validated_data.get('password'))
        return super(UserSerializer, self).update(validated_data)


class ChangePasswordSerializer(serializers.Serializer):
    currentPassword = serializers.CharField(allow_blank=False)
    newPassword = serializers.CharField(allow_blank=False)

    def validate(self, attrs):
        validate_password(password=attrs["newPassword"])
        return attrs


class ResetPasswordSerializer(serializers.Serializer):
    user_id = serializers.IntegerField()
    new_password = serializers.CharField(write_only=True)

    def validate_user_id(self, value):
        if not User.objects.filter(pk=value).exists():
            raise serializers.ValidationError("User with this ID does not exist.")
        return value


class SignupSerializer(serializers.ModelSerializer):
    user = UserSerializer(many=False)

    class Meta:
        model = Profile
        fields = '__all__'

    def create(self, validated_data):
        request = self.context['request']
        referer = request.META.get('HTTP_REFERER', '')
        token = getPlatformLinkByUrl(referer)
        # host = self.context['request'].query_params.get('host', '')

        if 'email_otp' not in self.initial_data.keys(): 
            error = {
                "data": {"error": "Please provide the valid OTP"}, "status": 400}
            raise serializers.ValidationError(error)
        otp = self.initial_data['email_otp']
        email = validated_data['user']['email']
        email_otp = EmailOtp.objects.filter(email=email, otp=otp).last()
        if not email_otp:
            error = {
                "data": {"error": "Please provide the valid OTP"}, "status": 400}
            raise serializers.ValidationError(error)

        if email_otp and timezone.now() > email_otp.date_expired:
            error = {"data": {"error": "OTP is expired"}, "status": 400}
            raise serializers.ValidationError(error)
        user_validated_data = validated_data.pop('user')
        password = user_validated_data.get('password')
        regexPassword = '^(?=.{6,15}$)(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*\W).*$'
        if not (re.search(regexPassword, password)):
            error = {
                "data": {"error": "Please provide a valid Password"}, "status": 400}
            raise serializers.ValidationError(error)
        user_validated_data['password'] = make_password(password)
        user_object = User.objects.create(**user_validated_data)
        user_object.save()
        validated_data['user'] = user_object
        profile = Profile.objects.create(**validated_data)
        profile.created_by_process = 'SignupSerializer'
        return profile
    
class PasswordValidateMixin:
    def validate(self, data):
        token = data.get('token')

        # get token validation time
        password_reset_token_validation_time = get_password_reset_token_expiry_time()

        # find token
        try:
            reset_password_token = _get_object_or_404(ResetPasswordToken, key=token)
        except (TypeError, ValueError, ValidationError, Http404,
                ResetPasswordToken.DoesNotExist):
            raise Http404(_("The OTP password entered is not valid. Please check and try again."))

        # check expiry date
        expiry_date = reset_password_token.created_at + timedelta(
            hours=password_reset_token_validation_time)

        if timezone.now() > expiry_date:
            # delete expired token
            reset_password_token.delete()
            raise Http404(_("The token has expired"))
        return data
class PasswordTokenSerializer(PasswordValidateMixin, serializers.Serializer):
    password = serializers.CharField(label=_("Password"), style={'input_type': 'password'})
    token = serializers.CharField()