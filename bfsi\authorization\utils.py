from calendar import timegm
from django.utils.datetime_safe import datetime
from rest_framework_simplejwt.tokens import RefreshToken


def my_jwt_response_handler(user):
    """
    Create a custom response format for JWT tokens
    """
    refresh = RefreshToken.for_user(user)
    
    return {
        'refresh': str(refresh),
        'access': str(refresh.access_token)
    }


def get_custom_jwt_payload(user):
    """
    Create a custom JWT payload with specific user fields
    To use this, you'll need to configure SIMPLE_JWT settings 
    to use this function for token customization
    """
    # Get the base token for the user
    refresh = RefreshToken.for_user(user)
    
    # Customize the token payload
    refresh['email'] = user.email
    refresh['username'] = user.username
    
    # Add custom fields to access token as well (optional)
    refresh.access_token['email'] = user.email
    refresh.access_token['username'] = user.username
    
    return {
        'refresh': str(refresh),
        'access': str(refresh.access_token)
    }