# Generated by Django 3.2 on 2025-07-14 09:42

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('odr', '0029_alter_dispute_closed_date'),
        ('notice', '0046_auto_20250714_1512'),
        ('sendgridlogs', '0006_sendgridmail_is_co_borrower'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='sendgridmail',
            name='dispute_id',
        ),
        migrations.AddField(
            model_name='sendgridmail',
            name='dispute',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='odr.dispute'),
        ),
        migrations.AddField(
            model_name='sendgridmail',
            name='template',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='notice.template'),
        ),
    ]
