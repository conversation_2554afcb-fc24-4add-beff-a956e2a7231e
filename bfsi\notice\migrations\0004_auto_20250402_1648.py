# Generated by Django 3.2 on 2025-04-02 11:18

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('notice', '0003_auto_20250401_1709'),
    ]

    operations = [
        migrations.CreateModel(
            name='Campaign',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('excel_file_name', models.CharField(blank=True, max_length=255, null=True)),
                ('excel_file_uploaded', models.BooleanField(default=False)),
                ('number_of_cases_created', models.IntegerField(default=0)),
                ('pdf_file_name', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('pdf_file_uploaded', models.BooleanField(default=False)),
                ('renaming_and_splitting_done', models.Bo<PERSON>anField(default=False)),
                ('sent_to_whatsapp', models.BooleanField(default=False)),
                ('sent_to_email', models.BooleanField(default=False)),
                ('report_generated', models.BooleanField(default=False)),
                ('report_file_path', models.CharField(max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.DeleteModel(
            name='ExcelReports',
        ),
    ]
