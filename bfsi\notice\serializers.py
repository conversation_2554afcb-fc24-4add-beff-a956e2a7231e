from rest_framework import serializers
from notice.models import WhatsAppTemplate, EmailTemplate, Template, Campaign, Entry, Notice


class WhatsAppTemplateSerializer(serializers.ModelSerializer):
    class Meta:
        model = WhatsAppTemplate
        fields = '__all__'


class EntrySerializer(serializers.ModelSerializer):
    class Meta:
        model = Entry
        fields = '__all__'


class EmailTemplateSerializer(serializers.ModelSerializer):
    class Meta:
        model = EmailTemplate
        fields = '__all__'


class TemplateSerializer(serializers.ModelSerializer):
    whatsapp_template = WhatsAppTemplateSerializer(read_only=True)
    email_template = EmailTemplateSerializer(read_only=True)

    class Meta:
        model = Template
        fields = '__all__'


class NoticeSerializer(serializers.ModelSerializer):
    uploaded_by = serializers.SerializerMethodField()
    template_name = serializers.SerializerMethodField()

    class Meta:
        model = Notice
        fields = ['id', 'dispute', 'template', 'template_name', 'file_path', 'file_name', 'notice_type', 'size', 'created_by', 'uploaded_by', 'created_at']

    def get_uploaded_by(self, obj):
        try:
            if obj.created_by:
                return f"{obj.created_by.first_name} {obj.created_by.last_name}"
        except (AttributeError, TypeError):
            pass
        return None

    def get_template_name(self, obj):
        try:
            if obj.template:
                return obj.template.name
        except (AttributeError, TypeError):
            pass
        return None


class CampaignDetailSerializer(serializers.ModelSerializer):

    client_name = serializers.SerializerMethodField()
    excel_display_name = serializers.SerializerMethodField()

    def get_client_name(self,obj):
        return f"{obj.client.user.first_name} {obj.client.user.last_name}"
    
    def get_excel_display_name(self,obj):
        return obj.excel_file_name.split('/')[-1]

    class Meta:
        model = Campaign
        fields = ['excel_file_name','excel_display_name','created_at','created_by','number_of_cases_created','client_name','client','flow_type','has_co_borrowers']
