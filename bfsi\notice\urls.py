from django.urls import path

from notice.views import *

urlpatterns = [
    path('api/renaming-and-splitting/', TriggerRenamingAndSplittingView.as_view(), name="upload-files"),
    path('api/template/<int:template_id>/splitting-stream/', template_splitting_stream, name='template_splitting_stream'),
    path('whatsapp/report/', download_report, name='whatsapp_report'),
    # path('api/split-pdf/', SplitPDFView.as_view(), name='split-pdf'),
    path('api/generate-signed-url/<int:campaign_id>/<int:template_id>/', GeneratePresignedURLView.as_view(), name='generate-signed-url'),
    path('api/download-zip/<int:campaign_id>/<int:template_id>/', DownloadDocumentsZipView.as_view(), name='download-documents-zip'),
    path('whatsappwebhook/', WhatsappWebhookView.as_view()),
    path('api/combined-report/', CombinedReportView.as_view(), name='combined-report'),
    path('api/reports/generate/whatsapp/', TriggerReportGenerationView.as_view(), name='generate_report'),

    # WhatsApp Templates
    path('api/whatsapp-templates/', WhatsAppTemplates.as_view(), name="whatsapp-templates"), # get all templates
    path('api/whatsapp-templates/<int:template_id>/', WhatsAppTemplates.as_view(), name="whatsapp-template-detail"), # get, update, delete a specific template
    path('api/profiles/<int:profile_id>/whatsapp-templates/', WhatsAppTemplates.as_view(), name="profile-whatsapp-templates"), # get the whatsapp templates linked to a client_profile

    # Email Templates
    path('api/email-templates/', EmailTemplates.as_view(), name="email-templates"), # get all templates
    path('api/email-templates/<int:template_id>/', EmailTemplates.as_view(), name="email-template-detail"), # get, update, delete a specific template
    path('api/profiles/<int:profile_id>/email-templates/', EmailTemplates.as_view(), name="profile-email-templates"), # get the email templates linked to a profile

    # Parent Templates
    path('api/templates/', TemplatesView.as_view(), name="templates"), # get all templates
    path('api/templates/<int:template_id>/', TemplatesView.as_view(), name="template-detail"), # get, update, delete a specific template
    path('api/campaigns/<int:campaign_id>/templates/', TemplatesView.as_view(), name="campaign-templates"), # get the templates linked to a campaign
    path('api/profiles/<int:user_id>/templates/', TemplatesView.as_view(), name="profile-templates"), # get the templates linked to a profile

    path('api/trigger-send-whatsapp-message/', TriggerWhatsAppSendMessageView.as_view(), name="trigger-whatsapp-send-message"),
    path('api/campaigns/<int:campaign_id>/templates/<int:template_id>/<str:message_type>/', campaign_message_sent_progress_stream, name='campaign_whatsapp_sent_progress_stream'),

    path('api/trigger-send-emails/', TriggerEmailSendMessageView.as_view(), name="trigger-emails-send"),
    path('api/campaigns/<int:campaign_id>/templates/<int:template_id>/<str:message_type>/', campaign_message_sent_progress_stream, name='campaign_email_sent_progress_stream'),

    # Dashboard APIs
    path('api/dashboard/', DashboardBulkActionsView.as_view(), name='dashboard'),
    path('api/dashboard/campaigns/<int:campaign_id>/', DashboardBulkActionsView.as_view(), name='dashboard-campaign-detail'),

    path('api/campaign/total-cases/<int:campaign_id>/', CampaignTotalCasesView.as_view(), name='campaign-total-cases'),

    # Dispute Notice Details
    path('api/disputes/<int:dispute_id>/notices/', DisputeNoticeDetailsView.as_view(), name='dispute-notice-details'),
    path('api/notices-for-campaign-template/<int:campaign_id>/<int:template_id>/', NoticesForCampaignTemplate.as_view(), name='notices-for-campaign-template'),

    # whatsapp and email test
    path('api/send-whatsapp-test/', SendWhatsAppTest.as_view(), name='send-whatsapp-test'),
    path('api/send-email-test/', SendEmailTest.as_view(), name='send-email-test'),

    path('api/send-pfl-termination-notice/', SendPFLPaymentRequestNotice.as_view(), name='send-pfl-termination-notice'),

    # campaign detail
    path('api/campaign/<int:campaign_id>/',CampaignDetailView.as_view(),name='campaign-detail')
]
