from odr.models import ProfileType, Dispute, Profile

def validate_profile_campaign(current_profile,campaign):
    # Check permissions based on user's profile type
    current_profile_type = current_profile.profile_type
    if current_profile_type == ProfileType.admin.name:
        # <PERSON><PERSON> can access any campaign
        pass
    elif current_profile_type == ProfileType.client.name:
        if not campaign.client or campaign.client.id != current_profile.id:
            return False
    elif current_profile_type == ProfileType.sub_client.name:
        # Client and sub_client can only access their campaigns
        client_profile = Profile.objects.get(id=current_profile.parent_client.id)
        if not campaign.client or campaign.client.id != client_profile.id:
            return False
    elif current_profile_type == ProfileType.case_manager.name:
        # Case manager can only access campaigns for disputes they're assigned to
        # Check if any disputes in this campaign are assigned to this case manager
        campaign_disputes = Dispute.objects.filter(campaign=campaign)
        has_access = False
        for dispute in campaign_disputes:      
            if current_profile in dispute.case_manager_rv.all():              
                has_access = True
                break
        if not has_access:
            return False
    elif current_profile_type == ProfileType.arbitrator.name:
        # Arbitrator can only access campaigns for disputes they're assigned to
        # Check if any disputes in this campaign are assigned to this arbitrator
        campaign_disputes = Dispute.objects.filter(campaign=campaign)
        has_access = False
        for dispute in campaign_disputes:
            if current_profile in dispute.arbitrator_rv.all():            
                has_access = True
                break
        if not has_access:
            return False
    elif current_profile_type not in ["sub_client","arbitrator","case_manager","client", "admin"]:
        return False
    return True
        
def validate_profile_dispute(current_profile,dispute):
    current_profile_type = current_profile.profile_type
    if current_profile_type == ProfileType.admin.name:
        # Admin can access any dispute
        pass
    elif current_profile_type == ProfileType.client.name:
        # Client and sub_client can only access their disputes
        if dispute.client != current_profile:
            return False
    elif current_profile_type == ProfileType.sub_client.name:
        client = Profile.objects.get(id=current_profile.parent_client.id)
        if dispute.client != client:
            return False
    elif current_profile_type == ProfileType.case_manager.name:        
        # Case manager can only access disputes they're assigned to
        if current_profile not in dispute.case_manager_rv.all():        
            return False
    elif current_profile_type == ProfileType.arbitrator.name:
        # Arbitrator can only access disputes they're assigned to
        if current_profile not in dispute.arbitrator_rv.all():        
            return False
    elif current_profile_type not in ["sub_client","arbitrator","case_manager","client", "admin"]:
        return False
    return True

def validate_profile_notice(current_profile,notice):

    current_profile_type = current_profile.profile_type
    # Check permissions based on user's profile type
    if current_profile_type == ProfileType.admin.name:
        # Admin can access any notice
        pass
    elif current_profile_type == ProfileType.client.name:
        # Client and sub_client can only access notices for their disputes            
        if notice.dispute and (not notice.dispute.client or notice.dispute.client.id != current_profile.id):
            return False
    elif current_profile_type == ProfileType.sub_client.name:
        client_profile = Profile.objects.get(id=current_profile.parent_client.id)
        if notice.dispute and (not notice.dispute.client or notice.dispute.client.id != client_profile.id):
            return False
    elif current_profile_type == ProfileType.case_manager.name:
        # Case manager can only access notices for disputes they're assigned to            
        dispute = notice.dispute
        if dispute and current_profile not in dispute.case_manager_rv.all(): 
            return False
    elif current_profile_type == ProfileType.arbitrator.name:
        # Arbitrator can only access notices for disputes they're assigned to            
        if notice.dispute and current_profile not in notice.dispute.arbitrator_rv.all():
            return False
    elif current_profile_type not in ["sub_client","arbitrator","case_manager","client", "admin"]:
        return False
    return True

def validate_profile_loan_id(current_profile,loan_id):
    current_profile_type = current_profile.profile_type

    #currently there is no unique constraint for loan_id as yet. So 'filter' than 'get'
    disputes_with_loan_id = Dispute.objects.filter(loan_id=loan_id)
    if not disputes_with_loan_id.exists():
        return False
    
    if current_profile_type == ProfileType.admin.name:
        # Admin can access any dispute
        pass
    elif current_profile_type == ProfileType.client.name:
        # Client and sub_client can only access their disputes
        client_disputes = disputes_with_loan_id.filter(client=current_profile).exists()
        if client_disputes:
            return False
    elif current_profile_type == ProfileType.sub_client.name:
        client_disputes = disputes_with_loan_id.ilter(client=current_profile.parent_client).exists()
        if client_disputes:
            return False
    elif current_profile_type == ProfileType.case_manager.name:
        has_access = False
        for dispute in disputes_with_loan_id:
            if current_profile in dispute.case_manager_rv.all():
                has_access = True
                break
        if not has_access:
            return False
    elif current_profile_type == ProfileType.arbitrator.name:
        # Arbitrator can only access disputes they're assigned to
        has_access = False
        for dispute in disputes_with_loan_id:
            if dispute.filter(arbitrator_rv=current_profile).exists():
                has_access = True
                break
        if not has_access:
            return False       
    elif current_profile_type not in ["sub_client","arbitrator","case_manager","client", "admin"]:
        return False
    return True

def validate_profile_client(current_profile,client):
    current_profile_type = current_profile.profile_type
    
    disputes_with_client = Dispute.objects.filter(client=client)
    if not disputes_with_client.exists():
        return False
    
    if current_profile_type == ProfileType.admin.name:
        # Admin can access any dispute
        pass
    elif current_profile_type == ProfileType.client.name:
        # Client and sub_client can only access their disputes
        if current_profile.id != client.id:
            return False
    elif current_profile_type == ProfileType.sub_client.name:
        if current_profile.parent_client.id != client.id:
            return False
    elif current_profile_type == ProfileType.case_manager.name:
        has_access = False
        for dispute in disputes_with_client:
            if current_profile in dispute.case_manager_rv.all():            
                has_access = True
                break
        if not has_access:
            return False
    elif current_profile_type == ProfileType.arbitrator.name:
        # Arbitrator can only access disputes they're assigned to
        has_access = False
        for dispute in disputes_with_client:
            if current_profile in dispute.arbitrator_rv.all():  
                has_access = True
                break
        if not has_access:
            return False       
    elif current_profile_type not in ["sub_client","arbitrator","case_manager","client", "admin"]:
        return False
    return True


def validate_profile_case_manager(current_profile,case_manager):
    '''
    Validates if current_profile can access disputes of case_manager
    '''
    current_profile_type = current_profile.profile_type
    
    disputes_with_case_manager = Dispute.objects.filter(case_manager_rv=case_manager)
    if not disputes_with_case_manager.exists():
        return False
    
    if current_profile_type == ProfileType.admin.name:
        # Admin can access any dispute
        pass
    elif current_profile_type == ProfileType.client.name:
        if not disputes_with_case_manager.filter(client=current_profile).exists():        
            return False
    elif current_profile_type == ProfileType.sub_client.name:
        if not disputes_with_case_manager.filter(client=current_profile.parent_client).exists():        
            return False
    elif current_profile_type == ProfileType.case_manager.name:
        if current_profile.id != case_manager.id:
            return False
    elif current_profile_type == ProfileType.arbitrator.name:
        # Arbitrator can only access disputes they're assigned to
        has_access = False
        for dispute in disputes_with_case_manager:
            if dispute.arbitrator_rv.filter(id=current_profile.id).exists():
                has_access = True
                break
        if not has_access:
            return False       
    elif current_profile_type not in ["sub_client","arbitrator","case_manager","client", "admin"]:
        return False
    return True

def validate_profile_arbitrator(current_profile,arbitrator):
    '''
    Validates if current_profile can access disputes of arbitrator
    '''
    current_profile_type = current_profile.profile_type
    
    disputes_with_arbitrator = Dispute.objects.filter(arbitrator_rv=arbitrator)
    if not disputes_with_arbitrator.exists():
        return False
    
    if current_profile_type == ProfileType.admin.name:
        # Admin can access any dispute
        pass
    elif current_profile_type == ProfileType.client.name:
        if not disputes_with_arbitrator.filter(client=current_profile).exists():        
            return False
    elif current_profile_type == ProfileType.sub_client.name:
        if not disputes_with_arbitrator.filter(client=current_profile.parent_client).exists():        
            return False
    elif current_profile_type == ProfileType.case_manager.name:
        has_access = False
        for dispute in disputes_with_arbitrator:
            if current_profile in dispute.case_manager_rv.all():            
                has_access = True
                break
        if not has_access:
            return False             
    elif current_profile_type == ProfileType.arbitrator.name:
        if current_profile.id != arbitrator.id:
            return False     
    elif current_profile_type not in ["sub_client","arbitrator","case_manager","client", "admin"]:
        return False
    return True