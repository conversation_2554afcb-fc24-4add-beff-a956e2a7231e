# Generated by Django 3.2 on 2025-04-29 14:22

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('notice', '0024_delete_whatsappbatchjob'),
    ]

    operations = [
        migrations.AddField(
            model_name='template',
            name='splits_processed',
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
        migrations.AddField(
            model_name='template',
            name='splitting_errors',
            field=models.JSONField(blank=True, default=list, null=True),
        ),
        migrations.AddField(
            model_name='template',
            name='splitting_status',
            field=models.CharField(blank=True, default='pending', max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='template',
            name='total_splits_expected',
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
    ]
