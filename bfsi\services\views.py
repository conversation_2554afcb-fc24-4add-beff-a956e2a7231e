"""
SMS Views for BFSI Backend

This module provides REST API endpoints for SMS functionality using Infobip.
Mimics the structure of WhatsApp API views for consistency.

Compatible with:
- Python 3.9.0
- Django 3.2
- Django REST Framework
- Infobip API

Author: BFSI Backend Team
"""

import json
import logging
from datetime import datetime
from django.utils import timezone
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.parsers import MultiPartParser, FormParser

from .infobip_helper import send_sms_simple, send_sms_bulk, send_sms_with_template, validate_sms_config
from bfsi.settings import db_logger

logger = logging.getLogger(__name__)


class SendSMSView(APIView):
    """
    Send single SMS message.
    Supports both regular messages and template-based messages.

    POST /api/infobip/sms/send

    Regular message:
    {
        "to": "+917016773450",
        "message": "Your message here"
    }

    Template-based message:
    {
        "to": "+917016773450",
        "template_id": "#200000000201690",
        "template_data": {"name": "<PERSON>", "amount": "5000"}  // optional
    }
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            # Extract data from request
            mobile = request.data.get('to') or request.data.get('phone_number')
            message = request.data.get('message')
            template_id = request.data.get('template_id')
            template_data = request.data.get('template_data')
            sender_id = request.data.get('sender_id')
            test_mode = request.data.get('test_mode', False)  # Add test mode support

            # Validate required fields
            if not mobile:
                return Response(
                    {"error": "'to' field is required."},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Check if either message or template_id is provided
            if not message and not template_id:
                return Response(
                    {"error": "Either 'message' or 'template_id' is required."},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Send SMS based on type
            if template_id:

                # Template-based SMS
                response = send_sms_with_template(
                    mobile=mobile,
                    template_id=template_id,
                    template_data=template_data,
                    sender_id=sender_id
                )
                message_type = "template"
                content_info = {"template_id": template_id, "template_data": template_data}
            else:
                # Regular SMS
                response = send_sms_simple(
                    mobile=mobile,
                    message=message,
                    sender_id=sender_id
                )
                message_type = "text"
                content_info = {"text": message}

            if response:
                # Parse response to get message details
                try:
                    response_data = json.loads(response)
                    db_logger.info(f"SMS ({message_type}) sent successfully to {mobile}")

                    return Response({
                        "message": f"SMS sent successfully ({message_type})",
                        "infobip_response": response_data,
                        "to": mobile,
                        "message_type": message_type,
                        **content_info
                    }, status=status.HTTP_200_OK)

                except json.JSONDecodeError:
                    return Response({
                        "message": f"SMS sent successfully ({message_type})",
                        "infobip_response": response,
                        "to": mobile,
                        "message_type": message_type,
                        **content_info
                    }, status=status.HTTP_200_OK)
            else:
                db_logger.error(f"SMS ({message_type}) failed to {mobile}")
                return Response(
                    {"error": f"Failed to send SMS ({message_type})"},
                    status=status.HTTP_400_BAD_REQUEST
                )

        except Exception as e:
            error_msg = f"Error in SendSMSView: {str(e)}"
            logger.error(error_msg)
            db_logger.exception(error_msg)
            return Response(
                {"error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class SendBulkSMSView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            # Extract data from request
            recipients = request.data.get('recipients', [])
            sender_id = request.data.get('sender_id')

            # Validate required fields
            if not recipients:
                return Response(
                    {"error": "recipients list is required"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Prepare recipients for bulk sending
            bulk_recipients = []
            for recipient in recipients:
                mobile = recipient.get('to') or recipient.get('phone_number')
                message = recipient.get('message')

                if mobile and message:
                    bulk_recipients.append({
                        'mobile': mobile,
                        'message': message
                    })

            if not bulk_recipients:
                return Response(
                    {"error": "No valid recipients found"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Send bulk SMS using helper function
            response = send_sms_bulk(bulk_recipients, sender_id)

            if response:
                try:
                    response_data = json.loads(response)
                    db_logger.info(f"Bulk SMS sent successfully to {len(bulk_recipients)} recipients")

                    return Response({
                        "message": "Bulk SMS sent successfully",
                        "infobip_response": response_data,
                        "recipients_count": len(bulk_recipients)
                    }, status=status.HTTP_200_OK)

                except json.JSONDecodeError:
                    return Response({
                        "message": "Bulk SMS sent successfully",
                        "infobip_response": response,
                        "recipients_count": len(bulk_recipients)
                    }, status=status.HTTP_200_OK)
            else:
                db_logger.error(f"Bulk SMS failed for {len(bulk_recipients)} recipients")
                return Response(
                    {"error": "Failed to send bulk SMS"},
                    status=status.HTTP_400_BAD_REQUEST
                )

        except Exception as e:
            error_msg = f"Error in SendBulkSMSView: {str(e)}"
            logger.error(error_msg)
            db_logger.exception(error_msg)
            return Response(
                {"error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class SMSStatusView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            # Get configuration status using helper function
            config_status = validate_sms_config()

            status_info = {
                'service': 'Infobip SMS',
                'status': 'active' if config_status['api_key_configured'] else 'inactive',
                'configuration': config_status,
                'timestamp': timezone.now().isoformat()
            }

            return Response(status_info, status=status.HTTP_200_OK)

        except Exception as e:
            error_msg = f"Error getting SMS status: {str(e)}"
            logger.error(error_msg)
            db_logger.exception(error_msg)
            return Response(
                {"error": "Failed to get SMS status"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class SendSMSTemplateView(APIView):
    """
    Send SMS using Infobip template.
    Dedicated endpoint for template-based messaging.

    POST /api/infobip/sms/send-template
    {
        "to": "+917016773450",
        "template_id": "#200000000201690",
        "template_data": {"name": "John", "amount": "5000"}  // optional
    }
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            mobile = request.data.get('to') or request.data.get('phone_number')
            template_id = request.data.get('template_id')
            template_data = request.data.get('template_data')
            sender_id = request.data.get('sender_id')

            # Validate required fields
            if not mobile:
                return Response(
                    {"error": "'to' field is required"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            if not template_id:
                return Response(
                    {"error": "'template_id' field is required"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Use template_data as provided (can be None for templates without variables)

            # Send template SMS
            response = send_sms_with_template(
                mobile=mobile,
                template_id=template_id,
                template_data=template_data,
                sender_id=sender_id
            )

            if response:
                try:
                    response_data = json.loads(response)
                    db_logger.info(f"Template SMS sent successfully to {mobile} using {template_id}")

                    return Response({
                        "message": "Template SMS sent successfully",
                        "infobip_response": response_data,
                        "to": mobile,
                        "template_id": template_id,
                        "template_data": template_data
                    }, status=status.HTTP_200_OK)

                except json.JSONDecodeError:
                    return Response({
                        "message": "Template SMS sent successfully",
                        "infobip_response": response,
                        "to": mobile,
                        "template_id": template_id,
                        "template_data": template_data
                    }, status=status.HTTP_200_OK)
            else:
                db_logger.error(f"Template SMS failed to {mobile} using {template_id}")
                return Response(
                    {"error": "Failed to send template SMS"},
                    status=status.HTTP_400_BAD_REQUEST
                )

        except Exception as e:
            error_msg = f"Error in SendSMSTemplateView: {str(e)}"
            logger.error(error_msg)
            db_logger.exception(error_msg)
            return Response(
                {"error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


# Import unified messaging (no database changes required)
from .unified_messaging import (
    UnifiedMessagingService, UnifiedCampaignProcessor, ODRDisputeAutomation,
    MessageChannels, send_unified_message, send_dispute_notification
)


class UnifiedMessageSendView(APIView):
    """
    Unified message sending endpoint supporting SMS and WhatsApp.
    Uses existing database models - no migrations required.

    POST /api/unified/message/send
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            data = request.data

            # Initialize unified messaging service
            service = UnifiedMessagingService()

            # Send unified message
            result = service.send_unified_message(
                recipient_phone=data.get('recipient_phone'),
                recipient_name=data.get('recipient_name'),
                template_id=data.get('template_id'),
                template_data=data.get('template_data'),
                message_content=data.get('message_content'),
                preferred_channel=data.get('preferred_channel', MessageChannels.SMS),
                enable_fallback=data.get('enable_fallback', True),
                dispute_id=data.get('dispute_id')
            )

            if result['success']:
                return Response({
                    "message": "Message sent successfully via unified system",
                    "message_id": result['message_id'],
                    "channel_used": result['channel_used'],
                    "external_id": result.get('external_id'),
                    "response": result.get('response')
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    "error": "Failed to send unified message",
                    "details": result.get('error'),
                    "message_id": result.get('message_id')
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Error in UnifiedMessageSendView: {str(e)}")
            return Response(
                {"error": "Internal server error"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class UnifiedCampaignView(APIView):
    """
    Unified campaign processing endpoint with Excel import.
    Similar to WhatsApp "tigerrun" but supports SMS and WhatsApp.
    Uses existing SMSCampaign model - no migrations required.

    POST /api/unified/campaign/process
    """
    permission_classes = [IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]

    def post(self, request):
        try:
            # Extract campaign data
            campaign_name = request.data.get('campaign_name')
            template_id = request.data.get('template_id', '#200000000203406')
            primary_channel = request.data.get('primary_channel', MessageChannels.SMS)
            fallback_channel = request.data.get('fallback_channel', MessageChannels.WHATSAPP)
            excel_file = request.FILES.get('excel_file')

            if not campaign_name:
                return Response(
                    {"error": "campaign_name is required"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            if not excel_file:
                return Response(
                    {"error": "excel_file is required"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Read Excel file
            excel_data = excel_file.read()

            # Process campaign
            processor = UnifiedCampaignProcessor()
            result = processor.process_excel_campaign(
                excel_file_data=excel_data,
                campaign_name=campaign_name,
                template_id=template_id,
                primary_channel=primary_channel,
                fallback_channel=fallback_channel,
                created_by_user_id=request.user.id
            )

            if result['success']:
                return Response({
                    "message": "Campaign processed successfully",
                    "campaign_id": result['campaign_id'],
                    "total_recipients": result['total_recipients'],
                    "messages_sent": result['messages_sent'],
                    "messages_failed": result['messages_failed'],
                    "errors": result.get('errors', [])
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    "error": "Failed to process campaign",
                    "details": result.get('error')
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Error processing unified campaign: {str(e)}")
            return Response(
                {"error": "Internal server error"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class DisputeNotificationView(APIView):
    """
    ODR dispute automation endpoint.
    Sends automated notifications for arbitration processes.
    Uses existing Dispute model - no migrations required.

    POST /api/unified/dispute/notify
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            data = request.data
            dispute_id = data.get('dispute_id')
            automation_type = data.get('automation_type', 'DOCUMENT_UPLOAD')
            recipient_phone = data.get('recipient_phone', '7016773450')
            template_data = data.get('template_data')

            if not dispute_id:
                return Response(
                    {"error": "dispute_id is required"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Send dispute notification
            automation = ODRDisputeAutomation()
            result = automation.send_dispute_notification(
                dispute_id=dispute_id,
                automation_type=automation_type,
                recipient_phone=recipient_phone,
                template_data=template_data
            )

            if result['success']:
                return Response({
                    "message": "Dispute notification sent successfully",
                    "message_id": result['message_id'],
                    "channel_used": result['channel_used'],
                    "dispute_id": dispute_id,
                    "automation_type": automation_type
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    "error": "Failed to send dispute notification",
                    "details": result.get('error')
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Error in DisputeNotificationView: {str(e)}")
            return Response(
                {"error": "Internal server error"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class UnifiedQuickTestView(APIView):
    """
    Quick test endpoint for unified messaging system.
    Uses default phone number 7016773450 and template #200000000203406.
    No database changes required.

    POST /api/unified/test
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            data = request.data

            # Use provided values or defaults
            phone_number = data.get('phone_number', '7016773450')
            template_id = data.get('template_id', '#200000000203406')
            client_name = data.get('client_name', 'BFSI Financial Services')
            preferred_channel = data.get('preferred_channel', MessageChannels.SMS)

            # Send unified message using convenience function
            result = send_unified_message(
                phone_number=phone_number,
                template_id=template_id,
                template_data={'client_name': client_name},
                preferred_channel=preferred_channel
            )

            if result['success']:
                return Response({
                    "message": "Test message sent successfully via unified system",
                    "message_id": result['message_id'],
                    "channel_used": result['channel_used'],
                    "phone_number": phone_number,
                    "template_id": template_id,
                    "template_data": {'client_name': client_name},
                    "external_id": result.get('external_id')
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    "error": "Failed to send test message",
                    "details": result.get('error')
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Error in UnifiedQuickTestView: {str(e)}")
            return Response(
                {"error": "Internal server error"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )





# Unified Messaging Views

class UnifiedMessageSendView(APIView):
    """
    Unified message sending endpoint that supports SMS, WhatsApp, and Email.
    Provides intelligent channel selection and automatic fallback.

    POST /api/unified/message/send

    Request body:
    {
        "recipient_phone": "7016773450",
        "recipient_email": "<EMAIL>",  // optional
        "recipient_name": "John Doe",  // optional
        "template_id": "#200000000203406",
        "template_data": {"client_name": "BFSI Financial Services"},
        "message_content": "Plain text message",  // alternative to template
        "preferred_channel": "sms",  // sms, whatsapp, email
        "enable_fallback": true,
        "dispute_id": 123  // optional
    }
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            # Extract request data
            data = request.data

            # Get messaging service
            messaging_service = UnifiedMessagingService()

            # Send message
            result = messaging_service.send_message(
                recipient_phone=data.get('recipient_phone'),
                recipient_email=data.get('recipient_email'),
                recipient_name=data.get('recipient_name'),
                template_id=data.get('template_id'),
                template_data=data.get('template_data'),
                message_content=data.get('message_content'),
                preferred_channel=data.get('preferred_channel', MessageChannels.SMS),
                enable_fallback=data.get('enable_fallback', True),
                dispute_id=data.get('dispute_id'),
                campaign_id=data.get('campaign_id')
            )

            if result['success']:
                return Response({
                    "message": "Message sent successfully",
                    "message_id": result['message_id'],
                    "channel_used": result['channel_used'],
                    "external_id": result.get('external_id'),
                    "response": result.get('response')
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    "error": "Failed to send message",
                    "details": result.get('error'),
                    "message_id": result.get('message_id')
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Error in UnifiedMessageSendView: {str(e)}")
            db_logger.exception(f"Unified message sending error: {str(e)}")
            return Response(
                {"error": "Internal server error"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class DisputeNotificationView(APIView):
    """
    Send automated notifications for ODR dispute processes.

    POST /api/unified/dispute/notify

    Request body:
    {
        "dispute_id": 123,
        "automation_type": "DOCUMENT_UPLOAD",
        "recipient_phone": "7016773450",
        "template_data": {"client_name": "BFSI Financial Services"}
    }
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            data = request.data
            dispute_id = data.get('dispute_id')
            automation_type = data.get('automation_type')
            recipient_phone = data.get('recipient_phone', '7016773450')
            template_data = data.get('template_data')

            if not dispute_id:
                return Response(
                    {"error": "dispute_id is required"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            if not automation_type:
                return Response(
                    {"error": "automation_type is required"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Send dispute notification
            result = send_dispute_notification(
                dispute_id=dispute_id,
                automation_type=automation_type,
                recipient_phone=recipient_phone,
                template_data=template_data
            )

            if result['success']:
                return Response({
                    "message": "Dispute notification sent successfully",
                    "message_id": result['message_id'],
                    "channel_used": result['channel_used'],
                    "dispute_id": dispute_id
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    "error": "Failed to send dispute notification",
                    "details": result.get('error')
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Error in DisputeNotificationView: {str(e)}")
            return Response(
                {"error": "Internal server error"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class QuickTestView(APIView):
    """
    Quick test endpoint for unified messaging system.
    Uses default phone number 7016773450 and template #200000000203406.

    POST /api/unified/test

    Request body:
    {
        "phone_number": "7016773450",  // optional, defaults to test number
        "template_id": "#200000000203406",  // optional, defaults to current template
        "client_name": "BFSI Financial Services",  // optional
        "preferred_channel": "sms"  // optional, defaults to SMS
    }
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            data = request.data

            # Use provided values or defaults
            phone_number = data.get('phone_number', '7016773450')
            template_id = data.get('template_id', '#200000000203406')
            client_name = data.get('client_name', 'BFSI Financial Services')
            preferred_channel = data.get('preferred_channel', MessageChannels.SMS)

            # Prepare template data
            template_data = {
                'client_name': client_name
            }

            # Send unified message
            result = send_unified_message(
                phone_number=phone_number,
                template_id=template_id,
                template_data=template_data,
                preferred_channel=preferred_channel
            )

            if result['success']:
                return Response({
                    "message": "Test message sent successfully",
                    "message_id": result['message_id'],
                    "channel_used": result['channel_used'],
                    "phone_number": phone_number,
                    "template_id": template_id,
                    "template_data": template_data
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    "error": "Failed to send test message",
                    "details": result.get('error')
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Error in QuickTestView: {str(e)}")
            return Response(
                {"error": "Internal server error"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )