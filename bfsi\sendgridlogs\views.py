import datetime
import traceback

import pytz
import requests
from django.db import IntegrityError
from django.utils import timezone
from rest_framework import status, views
from rest_framework.response import Response
from rest_framework import generics
from odr.utils import db_logger
from sendgridlogs.models import SendgridEventEntry, SendgridMail
from bfsi.settings import DB_NAME, TIME_ZONE
from .serializers import SendgridEventEntrySerializer


# class SendGridWebhookView(views.APIView):
#     authentication_classes = []
#     permission_classes = []

#     def post(self, request):
#         db_logger.warning(f'Sendgrid webhook calledd : \nself : {self}\nrequest : {request}\nrequest.data : {request.data}')
#         try:
#             events_for_bfsi_dev = []
#             for event in request.data:
#                 x_message_id = event["sg_message_id"].split('.')[0]

#                 # check if SendgridMail exists for this event and also check category of event
#                 category = event["category"] if "category" in event else []
#                 sendgrid_mail = SendgridMail.objects.filter(
#                     x_message_id=x_message_id, email=event["email"]).first()
#                 if DB_NAME in category and sendgrid_mail:
#                     timestamp = timezone.make_aware(datetime.datetime.fromtimestamp(
#                         int(event["timestamp"])), pytz.timezone(TIME_ZONE))

#                     try:
#                         sendgrid_event_entry = SendgridEventEntry.objects.create(
#                             event=event["event"],
#                             sendgrid_mail=sendgrid_mail,
#                             timestamp=timestamp,
#                             sg_event_id=event["sg_event_id"],
#                             smtp_id=event["smtp-id"] if "smtp-id" in event else None
#                         )
#                         if "reason" in event:
#                             sendgrid_event_entry.reason = event["reason"]
#                             sendgrid_event_entry.save()
#                     except IntegrityError:
#                         traceback_str = traceback.format_exc()
#                         db_logger.exception(
#                             "Error in Sendgrid webhook" + traceback_str)
#                 elif "backend-bfsi-dev" in category and DB_NAME == "backend-bfsi-dev":
#                     events_for_bfsi_dev.append(event)
#                 # elif "backend-bfsi-prod" in category and DB_NAME == "backend-bfsi-prod":
#                 #     events_for_bfsi_prod.append(event)
#             if len(events_for_bfsi_dev) == 0:
#                 return Response(data={})
#             else:
#                 # send other events to other databases api as sendgrid only sends it to production
#                 response_bfsi_dev = requests.post(
#                     "https://bfsi-dev-api.webnyay.in/sendgridlogs/webhook/", json=events_for_bfsi_dev)
#                 # response_bfsi_prod = requests.post(
#                 #     "https://bfsi-prod-api.webnyay.in/sendgridlogs/webhook/", json=events_for_bfsi_prod)

#                 # check their status code so that sendgrid server can retry in case of failure
#                 if response_bfsi_dev.status_code == 200:
#                 # and response_bfsi_prod.status_code == 200:
#                     return Response(data={})
#                 else:
#                     return Response(status=status.HTTP_500_INTERNAL_SERVER_ERROR)
#             # send events to other databases
#         except Exception:
#             traceback_str = traceback.format_exc()
#             db_logger.exception("Error in Sendgrid webhook" + traceback_str)
#             return Response(status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class SendGridWebhookView(views.APIView):
    authentication_classes = []
    permission_classes = []

    def post(self, request):
        db_logger.warning(f'Sendgrid webhook called: \nrequest.data: {request.data}')
        try:
            # Organize events by intended environment
            events_for_current_db = []
            events_for_dev = []

            for event in request.data:
                x_message_id = event["sg_message_id"].split('.')[0]

                # Check if category exists in the event
                category = event.get("category", [])

                # Get SendgridMail for this event
                sendgrid_mail = SendgridMail.objects.filter(
                    x_message_id=x_message_id, email=event["email"]).first()

                # Determine where this event should be processed
                if DB_NAME in category and sendgrid_mail:
                    # This event is for the current database
                    events_for_current_db.append(event)
                elif "backend-bfsi-dev" in category:
                    # This event should be forwarded to dev environment
                    events_for_dev.append(event)

            # Process events for the current database
            for event in events_for_current_db:
                x_message_id = event["sg_message_id"].split('.')[0]
                sendgrid_mail = SendgridMail.objects.filter(
                    x_message_id=x_message_id, email=event["email"]).first()
                
                timestamp = timezone.make_aware(
                    datetime.datetime.fromtimestamp(int(event["timestamp"])), 
                    pytz.timezone(TIME_ZONE)
                )

                try:
                    sendgrid_event_entry = SendgridEventEntry.objects.create(
                        event=event["event"],
                        sendgrid_mail=sendgrid_mail,
                        timestamp=timestamp,
                        sg_event_id=event["sg_event_id"],
                        smtp_id=event.get("smtp-id")
                    )
                    if "reason" in event:
                        sendgrid_event_entry.reason = event["reason"]
                        sendgrid_event_entry.save()
                except IntegrityError:
                    traceback_str = traceback.format_exc()
                    db_logger.exception("Error in Sendgrid webhook: " + traceback_str)

            # Forward events to dev if necessary
            if events_for_dev and DB_NAME != "backend-bfsi-dev":
                try:
                    response_dev = requests.post(
                        "https://bfsi-dev-api.webnyay.in/sendgridlogs/webhook/", 
                        json=events_for_dev,
                        timeout=10  # Add timeout to prevent hanging
                    )

                    if response_dev.status_code != 200:
                        db_logger.error(f"Failed to forward events to dev: {response_dev.status_code}")
                        return Response(status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                except requests.RequestException as e:
                    db_logger.error(f"Error forwarding events to dev: {str(e)}")
                    return Response(status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
            return Response(data={})
            
        except Exception:
            traceback_str = traceback.format_exc()
            db_logger.exception("Error in Sendgrid webhook: " + traceback_str)
            return Response(status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class SendgridEventView(generics.CreateAPIView):
    serializer_class = SendgridEventEntrySerializer

    def post(self, request, *args, **kwargs):
        # Extract dispute_id from URL parameters
        dispute_id = request.data.get('dispute_id', [])

        # Extract a list of emails from the request data (assuming it's a list of emails)
        email_list = request.data.get('emails', [])

        entries = []

        for email in email_list:
            print("dispute_id", dispute_id)
            print("email_id", email)

            # Fetch SendgridMail entries based on email and dispute_id
            sendgrid_mails = SendgridMail.objects.filter(email=email, dispute_id=dispute_id)

            if not sendgrid_mails:
                # Skip to the next email if no entries found for the given email and dispute_id
                continue

            # Fetch unique x_message_ids for the given email and dispute_id combination
            unique_x_message_ids = sendgrid_mails.values_list('x_message_id', flat=True).distinct()

            # Fetch all events for each unique x_message_id
            email_entries = []
            for x_message_id in unique_x_message_ids:
                events_for_x_message_id = SendgridEventEntry.objects.filter(sendgrid_mail__x_message_id=x_message_id)
                events_data = []

                for event in events_for_x_message_id:
                    timestamp_ist = event.timestamp.astimezone(timezone.get_current_timezone())
                    formatted_timestamp = timestamp_ist.strftime("%d/%m/%Y, %I:%M %p")
                    events_data.append({
                        'event': event.event,
                        'timestamp': formatted_timestamp,
                        'reason': event.reason,
                    })

                # Fetch the subject from the corresponding SendgridMail entry
                sendgrid_mail = sendgrid_mails.filter(x_message_id=x_message_id).first()
                subject = sendgrid_mail.subject if sendgrid_mail else None

                email_entries.append({
                    'x_message_id': x_message_id,
                    'subject': subject,
                    'events': events_data,
                })

            if email_entries:
                # Include entries for the current email in the overall entries list
                entries.append({
                    'email': email,
                    'entries': email_entries,
                })

        if not entries:
            return Response({'error': 'No events found for the given dispute_id and emails'})

        # Include all entries in the response
        response_data = {
            'dispute_id': dispute_id,
            'email_logs': entries,
        }

        return Response(response_data)
