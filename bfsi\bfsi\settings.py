"""
Django settings for bfsi project.

Generated by 'django-admin startproject' using Django 5.1.6.

For more information on this file, see
https://docs.djangoproject.com/en/5.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.1/ref/settings/
"""

from datetime import timedelta
import json
from pathlib import Path
import os
from dotenv import load_dotenv
import logging

from google.oauth2 import service_account

from odr.helperfunctions.gcp import GCPCredentialsManager

# Load environment variables
load_dotenv()

SEND_EMAIL = os.getenv('SEND_EMAIL', False)

DB_NAME = "backend-bfsi-"+os.getenv('ENV', "dev")
if os.getenv('ENV', 'dev') == 'dev':
    DB_NAME = "backend-bfsi-dev"
else:
    DB_NAME = "backend-bfsi-prod"
# DB_NAME = "backend-hotstar-prod"
# DB_NAME = "backend-hotstar-stage"
print('\nDB_NAME-->', DB_NAME)
print('SEND_EMAIL-->', SEND_EMAIL)
print('ENV-->', os.getenv('ENV'), '\n')

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-)5ru&ily7_5t$5qbs$=tk^gam)(undoxhdl@al$**x_#y3b5)*'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ['*']


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework',
    'rest_framework_simplejwt',
    'rest_framework_simplejwt.token_blacklist',
    'corsheaders',
    'django_extensions',
    'django_db_logger',
    'django_rest_passwordreset',
    'sendgridlogs',
    'odr',
    'authorization',
    'notice',
    'services',  # SMS and other services
]

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',  # CORS should always be first
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',   
]

ROOT_URLCONF = 'bfsi.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'bfsi.wsgi.application'


# Database
# https://docs.djangoproject.com/en/5.1/ref/settings/#databases
if os.getenv("USE_CLOUD_SQL_AUTH_PROXY", None):
    host = '127.0.0.1'
else:
    host = os.getenv('DB_HOST')

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',  # Change if using a different DB
        'NAME': os.getenv('DB_NAME'),
        'USER': os.getenv('DB_USER'),
        'PASSWORD': os.getenv('DB_PASSWORD'),
        'HOST': os.getenv('DB_HOST'),
        'PORT': os.getenv('DB_PORT'),
    }
}

# Password validation
# https://docs.djangoproject.com/en/5.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.1/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.1/howto/static-files/

STATIC_URL = 'static/'

# Default primary key field type
# https://docs.djangoproject.com/en/5.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

CORS_ALLOW_ALL_ORIGINS = True

db_logger = logging.getLogger('db')

# REST_FRAMEWORK = {
#     'DEFAULT_AUTHENTICATION_CLASSES': [
#         'rest_framework_jwt.authentication.JWTAuthentication',
#     ],
# }
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ),
}

SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(days=2),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=7),
    'ROTATE_REFRESH_TOKENS': False,
    'BLACKLIST_AFTER_ROTATION': True,
    'UPDATE_LAST_LOGIN': False,
    'ALGORITHM': 'HS256',
    'SIGNING_KEY': SECRET_KEY,
    'VERIFYING_KEY': None,
    'AUTH_HEADER_TYPES': ('Bearer',),
    'USER_ID_FIELD': 'id',
    'USER_ID_CLAIM': 'user_id',
    'AUTH_TOKEN_CLASSES': ('rest_framework_simplejwt.tokens.AccessToken',),
    'TOKEN_TYPE_CLAIM': 'token_type',
}


JWT_AUTH = {
    'JWT_RESPONSE_PAYLOAD_HANDLER': 'authorization.utils.my_jwt_response_handler',
    'JWT_AUTH_HEADER_PREFIX': 'Bearer',
    'JWT_EXPIRATION_DELTA': timedelta(days=2),
}

if os.getenv("ENV") not in ['prod', 'hotstar-prod']:
    GCS_BUCKET_NAME = 'webnyay-odr-uploads'
else:
    GCS_BUCKET_NAME = 'webnyay-odr-uploads-prod'

# GCP_PROJECT_ID = 'webnyay-ai'
PUBSUB_PDF_SPLITTER_TOPIC = 'bfsi-notices'
PUBSUB_WA_REPORT_TOPIC = 'whatsapp-report-generator'
PUBSUB_BULK_CASE_CREATION = 'bulk-case-creation'
PUBSUB_WHATSAPP_MESSAGES_TOPIC = 'send-whatsapp-messages'
PUBSUB_EMAILS_TOPIC = 'send-emails'

SECRET_NAME = 'bfsi'
GCS_PRESIGNED_URL_EXPIRATION = 60  # SECONDS
GCS_PRESIGNED_URL_MAX_EXPIRATION = 604800

credentials_dict = GCPCredentialsManager.setup_gcp_credentials()
STORAGE_CREDENTIALS = service_account.Credentials.from_service_account_info(
    credentials_dict)

secret_values = GCPCredentialsManager.get_secret_values(
    credentials_dict, SECRET_NAME)


SENDGRID_API_KEY = secret_values["SENDGRID_API_KEY"]
MAIL_ID = "<EMAIL>"
S21_MAIL_ID = "<EMAIL>"

AUTH_USER_MODEL = 'authorization.User'

db_logger = logging.getLogger('db')
WHATSAPP_CONFIG_BFSI = secret_values["WHATSAPP_CONFIG"]
IS_S21_WHATSAPP_CONFIG = secret_values["IS_S21_WHATSAPP_CONFIG"]

# Infobip SMS Configuration
# These settings can be overridden by environment variables for different environments
INFOBIP_API_KEY = os.getenv('INFOBIP_API_KEY') or secret_values.get("INFOBIP_API_KEY")
INFOBIP_BASE_URL = os.getenv('INFOBIP_BASE_URL', 'https://api.infobip.com')
INFOBIP_SENDER_ID = os.getenv('INFOBIP_SENDER_ID') or secret_values.get("INFOBIP_SENDER_ID", "InfoSMS")

# SMS Configuration
SMS_ENABLED = os.getenv('SMS_ENABLED', 'True').lower() == 'true'
SMS_MAX_RETRIES = int(os.getenv('SMS_MAX_RETRIES', '3'))
SMS_RETRY_DELAY = int(os.getenv('SMS_RETRY_DELAY', '5'))  # seconds


if os.getenv("ENV") == 'prod':
    GOOGLE_CREDENTIALS_JSON = os.getenv('GCP_PROD_CREDENTIALS')
else:
    GOOGLE_CREDENTIALS_JSON = os.getenv('GCP_DEV_CREDENTIALS')

if not GOOGLE_CREDENTIALS_JSON:
    raise EnvironmentError("GCP_CREDENTIALS environment variable not set.")

try:
    # Parse the single-line JSON string into a dictionary
    CREDENTIALS_DICT = json.loads(GOOGLE_CREDENTIALS_JSON)
except json.JSONDecodeError as e:
    raise ValueError(
        "Invalid JSON in GCP_CREDENTIALS environment variable.") from e

GCP_PROJECT_ID = CREDENTIALS_DICT.get("project_id")

# LOGGING = {
#     'version': 1,
#     'disable_existing_loggers': False,
#     'handlers': {
#         'console': {
#             'class': 'logging.StreamHandler',
#         },
#     },
#     'root': {
#         'handlers': ['console'],
#         'level': 'INFO',
#     },
# }
