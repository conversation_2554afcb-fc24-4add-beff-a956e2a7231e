from django.urls import path, re_path
from django.conf.urls import url

from authorization.apiviews import (ChangePassword,
                                    CustomPasswordResetView,
                                    CustomResetPasswordConfirm,
                                    GetOtpEmail,
                                    current_user,
                                    ResetPasswordView,
                                    CityByStateView,
                                    )
from authorization.views import SignupView, TokenAuthView,LogoutView

urlpatterns = [
     re_path(r'^api/user/$', current_user, name="create_user"),
     re_path(r'^api/password_reset/', CustomPasswordResetView.as_view()),
     re_path(r'^api/reset_password/change',
          CustomResetPasswordConfirm.as_view(), name='reset_password_change'),
     re_path(r'^api/changepassword', ChangePassword.as_view(), name="change_password"),
     re_path(r'^api/get-otp-email', GetOtpEmail.as_view(), name="get_otp-email"),
     url(r'^api/signup', SignupView.as_view(), name="register_user"),
     path('api/token-auth/', TokenAuthView.as_view(), name="token_auth"),
     path('api/user/reset-password/',
          ResetPasswordView.as_view(), name='reset-password'),
     path('api/cities/<str:state>/',
          CityByStateView.as_view(), name='cities-by-state'),
     path('api/logout/', LogoutView.as_view(), name="logout"),
     
]

