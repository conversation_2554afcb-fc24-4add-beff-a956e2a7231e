from bfsi import settings


def is_production_database():
    """
    Returns true if database is set to production
    """
    return settings.DB_NAME in {"backend-prod", "backend-hotstar-prod"}


def is_hotstar_database():
    """
    Returns true if database is set to hotstar
    """
    return settings.DB_NAME in {"backend-hotstar-prod", "backend-hotstar-stage"}


def get_database_name():
    """
    Returns the current database name
    """
    return settings.DB_NAME
