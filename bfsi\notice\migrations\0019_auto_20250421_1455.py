# Generated by Django 3.2 on 2025-04-21 09:25

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('notice', '0018_auto_20250418_1903'),
    ]

    operations = [
        migrations.RenameField(
            model_name='campaign',
            old_name='report_generated',
            new_name='case_creation_report_generated',
        ),
        migrations.RenameField(
            model_name='campaign',
            old_name='status',
            new_name='case_creation_status',
        ),
        migrations.RemoveField(
            model_name='campaign',
            name='pdf_file_name',
        ),
        migrations.RemoveField(
            model_name='campaign',
            name='pdf_file_uploaded',
        ),
        migrations.RemoveField(
            model_name='campaign',
            name='renaming_and_splitting_done',
        ),
        migrations.RemoveField(
            model_name='campaign',
            name='report_file_path',
        ),
        migrations.RemoveField(
            model_name='campaign',
            name='sent_to_email',
        ),
        migrations.RemoveField(
            model_name='campaign',
            name='sent_to_whatsapp',
        ),
        migrations.RemoveField(
            model_name='campaign',
            name='whatsapp_messages_sent',
        ),
        migrations.RemoveField(
            model_name='campaign',
            name='whatsapp_processed_rows',
        ),
        migrations.RemoveField(
            model_name='campaign',
            name='whatsapp_status',
        ),
        migrations.RemoveField(
            model_name='emailtemplate',
            name='campaign',
        ),
        migrations.RemoveField(
            model_name='emailtemplate',
            name='profile',
        ),
        migrations.RemoveField(
            model_name='emailtemplate',
            name='template_id',
        ),
        migrations.RemoveField(
            model_name='template',
            name='email_template',
        ),
        migrations.RemoveField(
            model_name='template',
            name='whatsapp_template',
        ),
        migrations.RemoveField(
            model_name='whatsapptemplate',
            name='campaign',
        ),
        migrations.RemoveField(
            model_name='whatsapptemplate',
            name='profile',
        ),
        migrations.RemoveField(
            model_name='whatsapptemplate',
            name='template_id',
        ),
        migrations.AddField(
            model_name='campaign',
            name='case_creation_report_file_path',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='emailtemplate',
            name='email_messages_sent',
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
        migrations.AddField(
            model_name='emailtemplate',
            name='email_processed_rows',
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
        migrations.AddField(
            model_name='emailtemplate',
            name='email_status',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='emailtemplate',
            name='email_template_id',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='emailtemplate',
            name='sent_to_email',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='emailtemplate',
            name='template',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='email_template', to='notice.template'),
        ),
        migrations.AddField(
            model_name='notice',
            name='template',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='notices', to='notice.template'),
        ),
        migrations.AddField(
            model_name='template',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='template',
            name='pdf_file_name',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='template',
            name='pdf_file_uploaded',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='template',
            name='renaming_and_splitting_done',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='whatsapptemplate',
            name='sent_to_whatsapp',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='whatsapptemplate',
            name='template',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='whatsapp_template', to='notice.template'),
        ),
        migrations.AddField(
            model_name='whatsapptemplate',
            name='whatsapp_messages_sent',
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
        migrations.AddField(
            model_name='whatsapptemplate',
            name='whatsapp_processed_rows',
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
        migrations.AddField(
            model_name='whatsapptemplate',
            name='whatsapp_status',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='whatsapptemplate',
            name='whatsapp_template_id',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='template',
            name='campaign',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='templates', to='notice.campaign'),
        ),
    ]
