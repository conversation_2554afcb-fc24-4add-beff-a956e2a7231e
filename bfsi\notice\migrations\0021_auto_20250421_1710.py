# Generated by Django 3.2 on 2025-04-21 11:40

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('notice', '0020_alter_template_campaign'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='emailtemplate',
            name='template',
        ),
        migrations.RemoveField(
            model_name='whatsapptemplate',
            name='template',
        ),
        migrations.AddField(
            model_name='template',
            name='email_template',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='parent_template', to='notice.emailtemplate'),
        ),
        migrations.AddField(
            model_name='template',
            name='whatsapp_template',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='parent_template', to='notice.whatsapptemplate'),
        ),
    ]
