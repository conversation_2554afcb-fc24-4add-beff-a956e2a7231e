# Generated by Django 3.2 on 2025-04-02 11:18

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('odr', '0007_auto_20250310_0053'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='dispute',
            name='customer_type',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='date_closed_by_grc',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='date_closed_by_gro',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='forward',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='forward_md',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='grc_ticket_number',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='gro_ticket_number',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='loan_or_booking_number',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='openable',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='organisation_name',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='platform_type',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='request_ticket_number',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='respondent_status',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='rv_tiac_payment_status',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='score',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='serious',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='sig',
        ),
        migrations.RemoveField(
            model_name='dispute',
            name='tags',
        ),
        migrations.RemoveField(
            model_name='profile',
            name='alternate_phone_number',
        ),
        migrations.RemoveField(
            model_name='profile',
            name='close_dispute_count',
        ),
        migrations.RemoveField(
            model_name='profile',
            name='open_dispute_count',
        ),
        migrations.AlterField(
            model_name='profile',
            name='auth_type',
            field=models.CharField(choices=[('local', 'local'), ('google', 'google')], default='local', max_length=300),
        ),
        migrations.DeleteModel(
            name='Signature',
        ),
        migrations.DeleteModel(
            name='Tag',
        ),
    ]
