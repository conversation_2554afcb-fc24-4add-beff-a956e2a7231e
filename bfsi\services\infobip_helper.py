"""
Infobip SMS Helper

This module provides SMS functionality using Infobip API, 
mimicking the structure of the existing WhatsApp helper.

Compatible with:
- Python 3.9.0
- Django 3.2
- Infobip API

Author: BFSI Backend Team
"""

import json
import requests
import logging
from bfsi.settings import INFOBIP_API_KEY, INFOBIP_BASE_URL, INFOBIP_SENDER_ID, db_logger

logger = logging.getLogger(__name__)


def send_sms_message(data):
    """
    Send SMS message using Infobip API.
    Mimics the WhatsApp send_message function structure.

    Args:
        data: JSON string containing SMS data

    Returns:
        Response text or None if failed
    """
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"App {INFOBIP_API_KEY}",
        "Accept": "application/json"
    }

    url = f"{INFOBIP_BASE_URL}/sms/2/text/advanced"

    try:
        response = requests.post(url, headers=headers, data=data, timeout=30)

        if response.status_code == 200:
            logger.info(f"SMS sent successfully. Status: {response.status_code}")
            logger.info(f"Response: {response.text}")
            return response.text
        else:
            logger.error(f"SMS failed. Status: {response.status_code}")
            logger.error(f"Response: {response.text}")

            # Check for flooding filter
            if "4104" in response.text or "FLOODING_FILTER" in response.text:
                logger.warning("Flooding filter triggered - too many messages to same number")
                db_logger.warning(f"Flooding filter: {response.text}")
            else:
                db_logger.exception(f"Error processing the SMS request: {response.text}")
            return None

    except requests.exceptions.RequestException as e:
        logger.error(f'SMS Connection Error: {str(e)}')
        db_logger.exception(f"SMS Connection Error: {str(e)}")
        return None


def send_sms_simple(mobile, message, sender_id=None):
    """
    Send a simple SMS message.
    Mimics the WhatsApp message sending pattern.

    Args:
        mobile: Recipient phone number (e.g., "+************")
        message: SMS message content
        sender_id: Custom sender ID (optional)

    Returns:
        Response text or None if failed
    """
    # Use default sender ID if not provided
    sender = sender_id or INFOBIP_SENDER_ID or "InfoSMS"

    # Validate phone number format
    if not mobile.startswith('+'):
        # Assume Indian number if no country code
        if len(mobile) == 10:
            mobile = '+91' + mobile
        elif len(mobile) == 12 and mobile.startswith('91'):
            mobile = '+' + mobile
        else:
            mobile = '+' + mobile

    # Create SMS payload
    data = {
        "messages": [
            {
                "from": sender,
                "destinations": [
                    {
                        "to": mobile
                    }
                ],
                "text": message
            }
        ]
    }

    return send_sms_message(json.dumps(data))


def send_template_message(data):
    """
    Send template SMS message using Infobip template API.

    Args:
        data: JSON string containing template SMS data

    Returns:
        Response text or None if failed
    """
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"App {INFOBIP_API_KEY}",
        "Accept": "application/json"
    }

    # Use the same endpoint as regular SMS - templates work with text/advanced
    url = f"{INFOBIP_BASE_URL}/sms/2/text/advanced"

    try:
        response = requests.post(url, headers=headers, data=data, timeout=30)

        if response.status_code == 200:
            logger.info(f"Template SMS sent successfully. Status: {response.status_code}")
            logger.info(f"Response: {response.text}")
            return response.text
        else:
            logger.error(f"Template SMS failed. Status: {response.status_code}")
            logger.error(f"Response: {response.text}")

            # Check for flooding filter
            if "4104" in response.text or "FLOODING_FILTER" in response.text:
                logger.warning("Flooding filter triggered - too many messages to same number")
                db_logger.warning(f"Flooding filter: {response.text}")
            else:
                db_logger.exception(f"Error processing the template SMS request: {response.text}")
            return None

    except requests.exceptions.RequestException as e:
        logger.error(f"Request failed: {e}")
        db_logger.exception(f"Template SMS request failed: {e}")
        return None


def send_sms_with_template(mobile, template_id, template_data=None, sender_id=None):
    """
    Send SMS using Infobip template.

    Args:
        mobile: Recipient phone number (e.g., "+************")
        template_id: Infobip template ID (e.g., "#200000000201690")
        template_data: Dictionary of template parameters (optional)
        sender_id: Custom sender ID (optional)

    Returns:
        Response text or None if failed
    """
    # Use default sender ID if not provided
    sender = sender_id or INFOBIP_SENDER_ID or "InfoSMS"

    # Validate phone number format
    if not mobile.startswith('+'):
        # Assume Indian number if no country code
        if len(mobile) == 10:
            mobile = '+91' + mobile
        elif len(mobile) == 12 and mobile.startswith('91'):
            mobile = '+' + mobile
        else:
            mobile = '+' + mobile

    # Create template-based SMS payload
    message_data = {
        "from": sender,
        "destinations": [
            {
                "to": mobile
            }
        ],
        "templateId": template_id
    }

    # Only include templateData if provided and not empty
    if template_data:
        message_data["templateData"] = template_data

    data = {
        "messages": [message_data]
    }

    return send_template_message(json.dumps(data))


def get_default_template_data(template_id):
    """
    Get default template data for production use.

    Args:
        template_id: Infobip template ID

    Returns:
        Dictionary with default template variables
    """
    # Default data for your arbitration template
    if template_id == "#200000000203406":
        return {
            "client_name": "BFSI Financial Services"
        }

    # Add more templates here as needed
    # elif template_id == "#another_template_id":
    #     return {
    #         "variable1": "default_value1",
    #         "variable2": "default_value2"
    #     }

    # Return default client name for unknown templates to prevent null values
    return {
        "client_name": "BFSI Financial Services"
    }


def send_sms_bulk(recipients, sender_id=None):
    """
    Send SMS to multiple recipients.
    
    Args:
        recipients: List of dictionaries with 'mobile' and 'message' keys
        sender_id: Custom sender ID (optional)
        
    Returns:
        Response text or None if failed
    """
    # Use default sender ID if not provided
    sender = sender_id or INFOBIP_SENDER_ID or "InfoSMS"
    
    messages = []
    for recipient in recipients:
        mobile = recipient.get('mobile')
        message = recipient.get('message')
        
        if not mobile or not message:
            logger.warning(f"Skipping recipient: missing mobile or message")
            continue
        
        # Validate phone number format
        if not mobile.startswith('+'):
            # Assume Indian number if no country code
            if len(mobile) == 10:
                mobile = '+91' + mobile
            elif len(mobile) == 12 and mobile.startswith('91'):
                mobile = '+' + mobile
            else:
                mobile = '+' + mobile
        
        messages.append({
            "from": sender,
            "destinations": [
                {
                    "to": mobile
                }
            ],
            "text": message
        })
    
    if not messages:
        logger.error("No valid recipients found for bulk SMS")
        return None
    
    data = {
        "messages": messages
    }
    
    return send_sms_message(json.dumps(data))


def validate_sms_config():
    """
    Validate SMS configuration.
    
    Returns:
        dict: Configuration status
    """
    config_status = {
        'api_key_configured': bool(INFOBIP_API_KEY),
        'base_url_configured': bool(INFOBIP_BASE_URL),
        'sender_id_configured': bool(INFOBIP_SENDER_ID),
        'base_url': INFOBIP_BASE_URL,
        'sender_id': INFOBIP_SENDER_ID
    }
    
    return config_status
