"""
Unified Messaging System for BFSI Backend

This module provides a unified interface for sending messages across SMS and WhatsApp
without requiring any database migrations. Uses existing models and in-memory structures.

Features:
- Unified messaging across SMS and WhatsApp
- Intelligent channel selection and fallback
- Campaign processing with Excel import
- ODR dispute automation
- Backward compatibility with existing systems

Author: BFSI Backend Team
"""

import json
import logging
import uuid
import pandas as pd
from io import BytesIO
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple, Any

from django.utils import timezone
from django.conf import settings

# Import existing helpers
from .infobip_helper import send_sms_with_template, send_sms_simple
from notice.whatsapp_helper import send_whatsapp_message_test

# Import existing models (no new models needed)
from .models import SMSMessage, SMSCampaign
from notice.models import Template
from odr.models import Dispute

logger = logging.getLogger(__name__)


class MessageChannels:
    """Message channel constants."""
    SMS = 'sms'
    WHATSAPP = 'whatsapp'
    EMAIL = 'email'


class MessageStatuses:
    """Message status constants."""
    PENDING = 'pending'
    SENT = 'sent'
    DELIVERED = 'delivered'
    FAILED = 'failed'


class UnifiedMessagingService:
    """
    Main service class for unified messaging across SMS and WhatsApp.
    Uses existing database models and in-memory tracking.
    """
    
    def __init__(self):
        self.default_phone_number = "7016773450"
        self.default_template_id = "#200000000203406"
        self.template_mapping = {
            # SMS template ID -> WhatsApp template name mapping
            "#200000000203406": "arbitration_document_upload_notice",
        }
    
    def send_unified_message(
        self,
        recipient_phone: str = None,
        recipient_name: str = None,
        template_id: str = None,
        template_data: Dict = None,
        message_content: str = None,
        preferred_channel: str = MessageChannels.SMS,
        enable_fallback: bool = True,
        dispute_id: int = None
    ) -> Dict[str, Any]:
        """
        Send a unified message with intelligent channel selection.
        
        Args:
            recipient_phone: Phone number (defaults to test number)
            recipient_name: Recipient name
            template_id: Template identifier
            template_data: Template variables
            message_content: Plain message content
            preferred_channel: Primary channel (sms/whatsapp)
            enable_fallback: Enable fallback to other channels
            dispute_id: Related dispute ID
            
        Returns:
            Dict with sending results
        """
        # Use defaults if not provided
        if not recipient_phone:
            recipient_phone = self.default_phone_number
        
        if not template_id and not message_content:
            template_id = self.default_template_id
            template_data = template_data or {"client_name": "BFSI Financial Services"}
        
        # Generate tracking ID
        message_id = str(uuid.uuid4())
        
        # Try primary channel
        result = self._send_via_channel(
            channel=preferred_channel,
            recipient_phone=recipient_phone,
            template_id=template_id,
            template_data=template_data,
            message_content=message_content
        )
        
        # Try fallback if primary failed
        if not result['success'] and enable_fallback:
            fallback_channel = self._get_fallback_channel(preferred_channel)
            if fallback_channel:
                logger.info(f"Primary {preferred_channel} failed, trying {fallback_channel}")
                result = self._send_via_channel(
                    channel=fallback_channel,
                    recipient_phone=recipient_phone,
                    template_id=template_id,
                    template_data=template_data,
                    message_content=message_content
                )
                result['channel_used'] = fallback_channel
            else:
                result['channel_used'] = preferred_channel
        else:
            result['channel_used'] = preferred_channel
        
        # Store in existing SMS model for tracking (reuse existing structure)
        if result['success']:
            try:
                SMSMessage.objects.create(
                    phone_number=recipient_phone,
                    message=message_content or f"Template: {template_id}",
                    status='sent',
                    external_message_id=result.get('external_id', ''),
                    api_response=result.get('response_data', {}),
                    dispute_id=dispute_id
                )
            except Exception as e:
                logger.warning(f"Could not store message in database: {e}")
        
        return {
            'success': result['success'],
            'message_id': message_id,
            'channel_used': result['channel_used'],
            'external_id': result.get('external_id'),
            'error': result.get('error'),
            'response': result.get('response_data')
        }
    
    def _send_via_channel(
        self,
        channel: str,
        recipient_phone: str,
        template_id: str = None,
        template_data: Dict = None,
        message_content: str = None
    ) -> Dict[str, Any]:
        """Send message via specific channel."""
        try:
            if channel == MessageChannels.SMS:
                return self._send_sms(recipient_phone, template_id, template_data, message_content)
            elif channel == MessageChannels.WHATSAPP:
                return self._send_whatsapp(recipient_phone, template_id, template_data, message_content)
            else:
                return {'success': False, 'error': f'Unsupported channel: {channel}'}
        except Exception as e:
            logger.error(f"Error sending via {channel}: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def _send_sms(
        self,
        recipient_phone: str,
        template_id: str = None,
        template_data: Dict = None,
        message_content: str = None
    ) -> Dict[str, Any]:
        """Send SMS using existing Infobip helper."""
        try:
            if template_id:
                response = send_sms_with_template(
                    mobile=recipient_phone,
                    template_id=template_id,
                    template_data=template_data
                )
            else:
                response = send_sms_simple(
                    mobile=recipient_phone,
                    message=message_content
                )
            
            if response:
                response_data = json.loads(response)
                external_id = response_data.get('messages', [{}])[0].get('messageId')
                return {
                    'success': True,
                    'external_id': external_id,
                    'response_data': response_data
                }
            else:
                return {'success': False, 'error': 'SMS sending failed'}
                
        except Exception as e:
            return {'success': False, 'error': f'SMS error: {str(e)}'}
    
    def _send_whatsapp(
        self,
        recipient_phone: str,
        template_id: str = None,
        template_data: Dict = None,
        message_content: str = None
    ) -> Dict[str, Any]:
        """Send WhatsApp using existing helper."""
        try:
            # Map SMS template to WhatsApp template
            whatsapp_template = self.template_mapping.get(template_id)
            if not whatsapp_template and template_id:
                return {'success': False, 'error': f'No WhatsApp template mapping for {template_id}'}
            
            # Extract client name for WhatsApp template
            client_name = None
            if template_data:
                client_name = template_data.get('client_name')
            
            response = send_whatsapp_message_test(
                mobile=recipient_phone,
                template_id=whatsapp_template or "arbitration_document_upload_notice",
                is_s21_notice=False,
                body_param_text=client_name,
                has_body_params=bool(client_name)
            )
            
            if response:
                response_data = json.loads(response)
                external_id = response_data.get('messages', [{}])[0].get('id')
                return {
                    'success': True,
                    'external_id': external_id,
                    'response_data': response_data
                }
            else:
                return {'success': False, 'error': 'WhatsApp sending failed'}
                
        except Exception as e:
            return {'success': False, 'error': f'WhatsApp error: {str(e)}'}
    
    def _get_fallback_channel(self, primary_channel: str) -> str:
        """Get fallback channel."""
        if primary_channel == MessageChannels.SMS:
            return MessageChannels.WHATSAPP
        elif primary_channel == MessageChannels.WHATSAPP:
            return MessageChannels.SMS
        return None


class UnifiedCampaignProcessor:
    """
    Campaign processor that handles Excel imports and batch messaging
    similar to existing WhatsApp "tigerrun" functionality.
    Uses existing SMSCampaign model for tracking.
    """
    
    def __init__(self):
        self.messaging_service = UnifiedMessagingService()
        self.batch_size = 50
    
    def process_excel_campaign(
        self,
        excel_file_data: bytes,
        campaign_name: str,
        template_id: str = "#200000000203406",
        primary_channel: str = MessageChannels.SMS,
        fallback_channel: str = MessageChannels.WHATSAPP,
        created_by_user_id: int = 1
    ) -> Dict[str, Any]:
        """
        Process campaign from Excel file using existing models.
        """
        try:
            # Parse Excel file
            df = self._parse_excel_file(excel_file_data)
            if df is None:
                return {'success': False, 'error': 'Failed to parse Excel file'}
            
            # Validate columns
            if not self._validate_excel_columns(df):
                return {'success': False, 'error': 'Invalid Excel format'}
            
            # Create campaign using existing SMSCampaign model
            campaign = SMSCampaign.objects.create(
                name=campaign_name,
                total_recipients=len(df),
                status='processing',
                created_by_id=created_by_user_id
            )
            
            # Process in batches
            results = self._process_batches(campaign, df, template_id, primary_channel, fallback_channel)
            
            # Update campaign status
            campaign.status = 'completed' if results['success'] else 'failed'
            campaign.messages_sent = results.get('sent', 0)
            campaign.messages_failed = results.get('failed', 0)
            campaign.save()
            
            return {
                'success': results['success'],
                'campaign_id': campaign.id,
                'total_recipients': len(df),
                'messages_sent': results.get('sent', 0),
                'messages_failed': results.get('failed', 0),
                'errors': results.get('errors', [])
            }
            
        except Exception as e:
            logger.error(f"Campaign processing error: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def _parse_excel_file(self, excel_file_data: bytes) -> pd.DataFrame:
        """Parse Excel file."""
        try:
            excel_bytes = BytesIO(excel_file_data)
            df = pd.read_excel(excel_bytes, engine='openpyxl')
            return df.fillna('')
        except Exception as e:
            logger.error(f"Excel parsing error: {str(e)}")
            return None
    
    def _validate_excel_columns(self, df: pd.DataFrame) -> bool:
        """Validate Excel columns."""
        required_columns = ['phone_number']
        return all(col in df.columns for col in required_columns)
    
    def _process_batches(
        self,
        campaign,
        df: pd.DataFrame,
        template_id: str,
        primary_channel: str,
        fallback_channel: str
    ) -> Dict[str, Any]:
        """Process Excel data in batches."""
        sent_count = 0
        failed_count = 0
        errors = []
        
        for index, row in df.iterrows():
            try:
                # Extract recipient data
                phone_number = str(row.get('phone_number', '')).strip()
                if not phone_number:
                    phone_number = self.messaging_service.default_phone_number
                
                # Prepare template data
                template_data = {
                    'client_name': str(row.get('client_name', 'BFSI Financial Services')).strip(),
                    'borrower_name': str(row.get('borrower_name', '')).strip(),
                    'loan_account_number': str(row.get('loan_account_number', '')).strip()
                }
                
                # Send message
                result = self.messaging_service.send_unified_message(
                    recipient_phone=phone_number,
                    template_id=template_id,
                    template_data=template_data,
                    preferred_channel=primary_channel,
                    enable_fallback=bool(fallback_channel)
                )
                
                if result['success']:
                    sent_count += 1
                else:
                    failed_count += 1
                    errors.append({
                        'row': index + 1,
                        'phone': phone_number,
                        'error': result.get('error')
                    })
                
            except Exception as e:
                failed_count += 1
                errors.append({
                    'row': index + 1,
                    'error': str(e)
                })
        
        return {
            'success': failed_count == 0,
            'sent': sent_count,
            'failed': failed_count,
            'errors': errors
        }


class ODRDisputeAutomation:
    """
    ODR dispute automation using existing models.
    Provides automated messaging for arbitration processes.
    """
    
    def __init__(self):
        self.messaging_service = UnifiedMessagingService()
        self.automation_templates = {
            'DOCUMENT_UPLOAD': "#200000000203406",
            'DEADLINE_REMINDER': "#200000000203406",
            'STATUS_UPDATE': "#200000000203406",
            'ARBITRATOR_ASSIGNED': "#200000000203406",
            'CASE_CLOSED': "#200000000203406"
        }
    
    def send_dispute_notification(
        self,
        dispute_id: int,
        automation_type: str,
        recipient_phone: str = "7016773450",
        template_data: Dict = None
    ) -> Dict[str, Any]:
        """Send automated dispute notification."""
        try:
            # Get dispute from existing model
            dispute = Dispute.objects.get(id=dispute_id)
            
            # Prepare template data with dispute info
            default_data = {
                "client_name": "BFSI Financial Services",
                "case_number": dispute.name,
                "dispute_id": str(dispute.id)
            }
            
            if template_data:
                default_data.update(template_data)
            
            # Get template for automation type
            template_id = self.automation_templates.get(automation_type, "#200000000203406")
            
            # Send notification
            result = self.messaging_service.send_unified_message(
                recipient_phone=recipient_phone,
                template_id=template_id,
                template_data=default_data,
                preferred_channel=MessageChannels.SMS,
                enable_fallback=True,
                dispute_id=dispute_id
            )
            
            return result
            
        except Dispute.DoesNotExist:
            return {'success': False, 'error': 'Dispute not found'}
        except Exception as e:
            logger.error(f"Dispute notification error: {str(e)}")
            return {'success': False, 'error': str(e)}


# Convenience functions for easy integration
def send_unified_message(
    phone_number: str = "7016773450",
    template_id: str = "#200000000203406",
    template_data: Dict = None,
    preferred_channel: str = MessageChannels.SMS
) -> Dict[str, Any]:
    """Convenience function for sending unified messages."""
    service = UnifiedMessagingService()
    return service.send_unified_message(
        recipient_phone=phone_number,
        template_id=template_id,
        template_data=template_data or {"client_name": "BFSI Financial Services"},
        preferred_channel=preferred_channel
    )


def process_excel_campaign(
    excel_file_data: bytes,
    campaign_name: str,
    template_id: str = "#200000000203406",
    primary_channel: str = MessageChannels.SMS
) -> Dict[str, Any]:
    """Convenience function for processing Excel campaigns."""
    processor = UnifiedCampaignProcessor()
    return processor.process_excel_campaign(
        excel_file_data=excel_file_data,
        campaign_name=campaign_name,
        template_id=template_id,
        primary_channel=primary_channel
    )


def send_dispute_notification(
    dispute_id: int,
    automation_type: str = "DOCUMENT_UPLOAD",
    recipient_phone: str = "7016773450"
) -> Dict[str, Any]:
    """Convenience function for dispute notifications."""
    automation = ODRDisputeAutomation()
    return automation.send_dispute_notification(
        dispute_id=dispute_id,
        automation_type=automation_type,
        recipient_phone=recipient_phone
    )
