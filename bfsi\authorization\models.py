from django.db import models
from django.contrib.auth.base_user import AbstractBaseUser, BaseUserManager
from django.contrib.auth.models import PermissionsMixin
from django.utils import timezone
from django_extensions.db.models import TimeStampedModel
from django.utils.translation import gettext_lazy as _

import random
import uuid


state_city_map = {
    "Andaman and Nicobar Islands": ["Port Blair", "Havelock", "Neil Island", "Little Andaman", "Ritchie's Archipelago"],
    "Andhra Pradesh": ["Visakhapatnam", "Vijayawada", "Tirupati", "Guntur", "Nellore", "Kakinada", "Rajahmundry", "Anantapur", "Chittoor", "Kadapa"],
    "Arunachal Pradesh": ["Itanagar", "Tawang", "Ziro", "Bomdila", "Naharlagun", "Tezpur", "Pasighat", "Aalo"],
    "Assam": ["Guwahati", "Dibrugarh", "Jorhat", "Nagaon", "Silchar", "Tezpur", "Bongaigaon", "Hailakandi", "Sivasagar", "Tinsukia", "Barpeta"],
    "Bihar": ["Patna", "Gaya", "Bhagalpur", "Muzaffarpur", "Purnia", "Buxar", "Begusarai", "Chapra", "Darbhanga", "Munger", "Sitamarhi", "Samastipur"],
    "Chandigarh": ["Chandigarh"],
    "Chhattisgarh": ["Raipur", "Bilaspur", "Durg", "Korba", "Raigarh", "Jagdalpur", "Ambikapur", "Bhilai"],
    "Dadra and Nagar Haveli and Daman and Diu": ["Daman", "Diu", "Silvassa"],
    "Delhi": ["Delhi", "New Delhi"],
    "Goa": ["Panaji", "Margao", "Vasco da Gama", "Mapusa", "Ponda", "Cortalim", "Bicholim"],
    "Gujarat": ["Ahmedabad", "Surat", "Vadodara", "Rajkot", "Bhavnagar", "Jamnagar", "Junagadh", "Nadiad", "Anand", "Mehsana", "Navsari"],
    "Haryana": ["Chandigarh", "Faridabad", "Gurugram", "Ambala", "Panipat", "Hisar", "Karnal", "Sonipat", "Rohtak", "Yamunanagar"],
    "Himachal Pradesh": ["Shimla", "Manali", "Dharamshala", "Kullu", "Solan", "Mandi", "Kangra", "Bilaspur", "Chamba", "Nahan"],
    "Jammu and Kashmir": ["Srinagar", "Jammu", "Sopore", "Anantnag", "Baramulla", "Kathua", "Udhampur", "Rajouri", "Poonch"],
    "Jharkhand": ["Ranchi", "Jamshedpur", "Dhanbad", "Hazaribagh", "Bokaro", "Deoghar", "Giridih", "Ramgarh", "Chaibasa", "Jhumri Telaiya"],
    "Karnataka": ["Bengaluru", "Mysuru", "Hubli", "Dharwad", "Mangalore", "Belgaum", "Tumkur", "Shimoga", "Bijapur", "Bidar", "Udupi"],
    "Kerala": ["Thiruvananthapuram", "Kochi", "Kozhikode", "Malappuram", "Kollam", "Thrissur", "Kannur", "Alappuzha", "Pathanamthitta", "Palakkad", "Idukki"],
    "Ladakh": ["Leh", "Kargil"],
    "Lakshadweep": ["Kavaratti", "Minicoy", "Agatti", "Andrott", "Kalapeni", "Suheli Par", "Bithra"],
    "Madhya Pradesh": ["Bhopal", "Indore", "Gwalior", "Jabalpur", "Ujjain", "Sagar", "Rewa", "Satna", "Khajuraho", "Dewas"],
    "Maharashtra": ["Mumbai", "Pune", "Nagpur", "Nashik", "Thane", "Aurangabad", "Solapur", "Kolhapur", "Navi Mumbai", "Satara", "Jalgaon", "Amravati", "Ratnagiri", "Latur"],
    "Manipur": ["Imphal", "Churachandpur", "Thoubal", "Kangpokpi", "Bishnupur", "Senapati", "Tamenglong"],
    "Meghalaya": ["Shillong", "Tura", "Jowai", "Nongstoin", "Williamnagar", "Mawlai", "Mawkyrwat"],
    "Mizoram": ["Aizawl", "Lunglei", "Champhai", "Siaha", "Serchhip", "Kolasib"],
    "Nagaland": ["Kohima", "Dimapur", "Mokokchung", "Mon", "Wokha", "Phek", "Tuensang", "Zunheboto"],
    "Odisha": ["Bhubaneswar", "Cuttack", "Berhampur", "Rourkela", "Sambalpur", "Balasore", "Bargarh", "Jharsuguda", "Puri"],
    "Puducherry": ["Puducherry", "Karaikal", "Mahe", "Yanam"],
    "Punjab": ["Amritsar", "Ludhiana", "Jalandhar", "Patiala", "Bathinda", "Mohali", "Pathankot", "Moga", "Hoshiarpur", "Firozpur"],
    "Rajasthan": ["Jaipur", "Jodhpur", "Udaipur", "Kota", "Ajmer", "Bikaner", "Alwar", "Bharatpur", "Sikar", "Barmer", "Pali", "Tonk", "Churu"],
    "Sikkim": ["Gangtok", "Namchi", "Gyalshing", "Mangan", "Rangpo"],
    "Tamil Nadu": ["Chennai", "Coimbatore", "Madurai", "Tiruchirappalli", "Salem", "Tirunelveli", "Erode", "Tiruppur", "Vellore", "Thoothukudi", "Dindigul", "Kanchipuram", "Cuddalore", "Kanyakumari", "Karur", "Villupuram", "Namakkal", "Sivakasi", "Ranipet", "Chidambaram", "Nagapattinam", "Pudukkottai", "Tiruvallur", "Tenkasi", "Ramanathapuram", "Tiruvarur", "Virudhunagar", "Arakkonam", "Ariyalur", "Bodinayakanur", "Chengalpattu", "Coonoor", "Dharmapuri", "Gudiyatham", "Kallakurichi", "Kalpakkam", "Kancheepuram", "Karaikal", "Karur", "Krishnagiri", "Madhurandhagam", "Mettur", "Mullainagar", "Nagercoil", "Nellai", "Perambalur", "Pollachi", "Pudukkottai", "Sankarankovil", "Sholavandan", "Sivaganga", "Thanjavur", "Theni", "Thirukovilur", "Thirumangalam", "Vellore", "Virudhunagar"],
    "Telangana": ["Hyderabad", "Warangal", "Nizamabad", "Karimnagar", "Khammam", "Mahbubnagar", "Adilabad", "Suryapet", "Ramagundam", "Nalgonda"],
    "Tripura": ["Agartala", "Dharmanagar", "Udaipur", "Kailashahar", "Khowai", "Amarpur", "Belonia"],
    "Uttar Pradesh": ["Lucknow", "Kanpur", "Ghaziabad", "Agra", "Varanasi", "Meerut", "Allahabad (Prayagraj)", "Bareilly", "Aligarh", "Moradabad", "Saharanpur", "Firozabad", "Jhansi", "Muzaffarnagar", "Mathura"],
    "Uttarakhand": ["Dehradun", "Haridwar", "Nainital", "Rishikesh", "Almora", "Haldwani", "Pithoragarh", "Rudrapur"],
    "West Bengal": ["Kolkata", "Howrah", "Durgapur", "Asansol", "Siliguri", "Darjeeling", "Bardhaman", "Kharagpur", "Midnapore", "Malda", "Jalpaiguri", "Berhampore"]
}


class LowercaseEmailField(models.EmailField):
    def to_python(self, value):
        value = super(LowercaseEmailField, self).to_python(value)
        if isinstance(value, str):
            return value.lower()
        return value


class UserManager(BaseUserManager):
    use_in_migrations = True

    def _create_user(self, email, password, **extra_fields):
        """
        Creates and saves a User with the given email and password.
        """
        if not email:
            raise ValueError('The given email must be set')
        email = self.normalize_email(email)
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_user(self, email, password=None, **extra_fields):
        extra_fields.setdefault('is_staff', False)
        extra_fields.setdefault('is_superuser', False)
        return self._create_user(email, password, **extra_fields)

    def create_superuser(self, email, password, **extra_fields):
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)

        if extra_fields.get('is_staff') is not True:
            raise ValueError('Superuser must have is_staff=True.')

        if extra_fields.get('is_superuser') is not True:
            raise ValueError('Superuser must have is_superuser=True.')

        return self._create_user(email, password, **extra_fields)


class User(AbstractBaseUser, PermissionsMixin):
    username = models.CharField(
        _('username'), max_length=100, default={str(uuid.uuid1())})
    email = LowercaseEmailField(unique=True)
    first_name = models.CharField(_('first name'), max_length=100, blank=True)
    last_name = models.CharField(_('last name'), max_length=100, blank=True)
    date_joined = models.DateTimeField(_('date joined'), auto_now_add=True)
    is_active = models.BooleanField(_('active'), default=True)
    temp_password_email_sent = models.BooleanField(default=False)
    is_staff = models.BooleanField(
        _('staff status'),
        default=False,
        help_text=_(
            'Designates whether the user can log into this admin site.'),
    )

    objects = UserManager()

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = []

    class Meta:
        verbose_name = _('user')
        verbose_name_plural = _('users')

    def __str__(self):
        return self.email

    def get_full_name(self):
        '''
        Returns the first_name plus the last_name, with a space in between.
        '''
        full_name = '%s %s' % (self.first_name, self.last_name)
        return full_name.strip()

    def get_short_name(self):
        '''
        Returns the short name for the user.
        '''
        return self.first_name


class EmailOtp(TimeStampedModel):
    email = models.EmailField(_('email address'), unique=False)
    name = models.CharField(_('first name'), max_length=60, blank=True)
    otp = models.CharField(_('otp'), max_length=60, blank=True, null=True)
    date_created = models.DateTimeField(_('date joined'), auto_now_add=True)
    date_expired = models.DateTimeField(_('date joined'), auto_now_add=True)
    is_active = models.BooleanField(_('active'), default=True)
    phone_number = models.CharField(_('phone number'), max_length=15, blank=True, null=True)


class OTP(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    otp = models.CharField(max_length=6)
    created_at = models.DateTimeField(auto_now_add=True)

    @classmethod
    def generate_otp(cls, user):
        otp = ''.join([str(random.randint(0, 9)) for _ in range(6)])
        return cls.objects.create(user=user, otp=otp)

    def is_valid(self, expiry_minutes=10):
        return timezone.now() < self.created_at + timezone.timedelta(minutes=expiry_minutes)

